<app-sidebar>
    <div class="content-wrapper fade-in">
        <div class="mx-3">
        <app-breadcrumb [breadCrumbModules]="breadCrumbModules" activeColor="text-gray-600"
            inActiveColor="text-gray-400">
        </app-breadcrumb>
    </div>
        <!-- Tabs navigation -->

        <ul class="nav nav-tabs mt-2 px-3" id="myTab" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link" [ngClass]="{'active': activeTab === 'clubdetails'}"
                    (click)="setActiveTab('clubdetails')">Club Details</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" [ngClass]="{'active': activeTab === 'meetings'}"
                    (click)="setActiveTab('meetings')">Meetings</button>
            </li>
        </ul>

        <div class="mt-4 ">

            <!-- clubdetails information -->
            <div class="tab-content" id="clubdetails">
                <div *ngIf="activeTab === 'clubdetails'" [formGroup]="clubForm" class="tab-pane fade show active"
                    id="clubdetails" role="tabpanel">
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <app-input-wrapper formControlName="bookClubName" id="bookClubName"
                                label="Name of the club" type="text" [isRequired]="false" [isReadonly]="true">
                            </app-input-wrapper>
                        </div>

                        <div class="col-md-8 mb-3">
                            <app-input-wrapper formControlName="memberReqPrompt" id="memberReqPrompt"
                                label="Member request prompt" type="text" [isRequired]="false" [isReadonly]="true">
                            </app-input-wrapper>
                        </div>

                    </div>

                    <div class="mb-3">
                        <label for="clubCharter" class="form-label">{{clubDetails?.bookClubType == "IMPROMPTU"
                            ?"Impromptu club charter":"Standing club charter"}}</label>
                        <textarea class="form-control form-text-area readonly" id="clubCharter" rows="7" formControlName="clubCharter"
                            readonly></textarea>
                    </div>
                </div>


                <div *ngIf="activeTab === 'meetings'" class="tab-pane fade show active" id="meetings" role="tabpanel">
                    <app-list-with-pagination [perPageItems]="limit" [p]="offset" idKey="meeting_id" [columns]="meetingColumns" [data]="clubMeetings"
                        [onCurrentPageChanged]="onCurrentPageChanged" [totalNumberOfRecords]="totalNumberOfRecords"
                        [onPageSizeChanged]="onPageSizeChanged"></app-list-with-pagination>
                    <br>
                </div>
            </div>
        </div>
    </div>
</app-sidebar>