import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';

@Component({
  selector: 'app-unauthorize-page',
  templateUrl: './unauthorize-page.component.html',
  styleUrls: ['./unauthorize-page.component.scss']
})
export class UnauthorizePageComponent implements OnInit {

  constructor(private router: Router) { }

  ngOnInit(): void {
  }

  logout() {
    localStorage.clear();
    this.router.navigate(["/login"]);
  }

}
