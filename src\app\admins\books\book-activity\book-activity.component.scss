.book-or-club-name {
    // font-weight: 700;
    width: auto;
    overflow: auto;
}

.listHeader {
    color: #253943 !important;
    height: auto;
    padding: 10px 15px !important;
    background-color: #fff5d6 !important;
    border: 1px solid #253943;
    border-bottom: none;
    border-top-left-radius: 10px !important;
    border-top-right-radius: 10px !important;
    border-bottom-left-radius: 0px !important;
    border-bottom-right-radius: 0px !important;
}


select {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    background: none;
    border: 1px solid #ccc;
}

.nameOrBtnRow {
    display: flex;
    flex-direction: row;
    gap: 20px;
    align-items: center;
    width: 100%;
}

.book-or-club-name {
    flex: 1;
    min-width: 0;
    align-self: center;
    font-size: 16px;
}

.download-btn {
    flex-shrink: 0;
}




@media screen and (max-width: 425px) {
    .nameOrBtnRow {
        flex-direction: column;
    }

}

select.form-control, select.asColorPicker-input, .dataTables_wrapper select, .jsgrid .jsgrid-table .jsgrid-filter-row select, .select2-container--default select.select2-selection--single, .select2-container--default .select2-selection--single select.select2-search__field, select.typeahead, select.tt-query, select.tt-hint {
    font-size: 16px !important;
}