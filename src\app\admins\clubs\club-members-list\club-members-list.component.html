<app-sidebar [menuTitle]="menuTitle">
    <div class="content-wrapper fade-in">

        <div class="row mb-4 head-Home">
            <div [ngClass]="[isLargeScreen?'col-md-2': 'col-md-4']">
                <app-breadcrumb [breadCrumbModules]="breadCrumbModules">
                </app-breadcrumb>
            </div>
            <div class="col-lg-3 position-relative">
                <app-search-box (searchValue)="onSearch($event)"></app-search-box>
            </div>
            <!-- <div class="col-lg-3 mb-2 mb-lg-0">
                <button type="submit" class="btn btn-primary w-100">Add New User</button>
            </div> -->
        </div>

        <app-list-with-pagination [perPageItems]="limit" [p]="offset" idKey="userId" [columns]="columns"
            [actionPermissions]="{ view: true, edit: false, delete: false, members:false }" [data]="bookClubMembersList"
            [onCurrentPageChanged]="onCurrentPageChanged" [onDeleteRecord]="deleteRecord"
            [totalNumberOfRecords]="totalNumberOfRecords" [onPageSizeChanged]="onPageSizeChanged"
            viewPageLink="/admins/clubs/profile" [module]="_module"></app-list-with-pagination>


    </div>
</app-sidebar>