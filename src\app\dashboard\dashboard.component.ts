import { Component, OnInit } from '@angular/core';
import { DataTransferService } from 'src/app/shared/services/data-transfer.service';
import { PrimeNGConfig } from 'primeng-lts/api';
import { Observable, Subscription } from 'rxjs';
import { AppConfig, AppConfigService } from '../app-config.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { Router } from '@angular/router';
import { ChartOptions, ChartType } from 'chart.js';
import { ToastrService } from 'ngx-toastr';
import { NgbDatepickerConfig } from '@ng-bootstrap/ng-bootstrap';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.scss'],
})
export class DashboardComponent implements OnInit {
  basicData: any;
  basicOptions: any;
  subscription: Subscription;
  selectedButton: string = 'users';

  config: AppConfig | undefined;
  fromDate: string = '';
  toDate: string = '';
  books = [
    { name: 'How to build a car', author: '<PERSON> Newey', count: 2800 },
    { name: '1984', author: 'George Orwell', count: 2450 },
    { name: 'Pride and Prejudice', author: 'Jane Austen', count: 2100 },
    { name: 'The Great Gatsby', author: 'F. <PERSON> <PERSON>', count: 1900 },
    { name: 'Moby <PERSON>', author: '<PERSON> Melville', count: 1854 },
  ];
  // selectedDays: string;
  // selectedMonth: string;
  // selectedWeek: number;
  count: any = [];
  data: string[] = []; // Declare data as an array of strings
  selectedSector: string = 'all';
  menuTitle: string = 'Dashboard';
  totalUsers: number = 0;
  totalBookClubs: number = 0;
  totalBooks: number = 0;
  chartLabels: any;
  chartData: { data: any; label: string }[];
  selectedData: any;
  toBeReadData: any;
  currentlyReadingData:any;
  popularsBooks: any;

  // isMobile: boolean;

  constructor(
    private dataTransferService: DataTransferService,
    private primengConfig: PrimeNGConfig,
    private configService: AppConfigService,
    private ngxSpinnerService: NgxSpinnerService,
    private http: HttpClient,
    private router: Router,
    private toastr: ToastrService,
    private datepickerConfig: NgbDatepickerConfig
  ) {
    
    this.setDates();
  }

  ngOnInit() {
    this.getUserChartData();
    this.setupChartOptions();
    this.getTop5Records();
    // this.getPopularBooks();
  }

  onDateChange(): void {
    console.log("this.fromDate&this.toDate",this.fromDate,this.toDate);
    
    if (!this.fromDate && !this.toDate) {
      this.setDates();
    } else if (!this.toDate) {
      const today = new Date();
      this.toDate = this.formatDate(today);
    } else if (!this.fromDate) {
      const today = new Date();
      const lastMonthDate = new Date(today);
      lastMonthDate.setMonth(lastMonthDate.getMonth() - 1);
      if (lastMonthDate.getMonth() !== (today.getMonth() - 1 + 12) % 12) {
        lastMonthDate.setDate(0); 
      }
      this.fromDate = this.formatDate(lastMonthDate);
    }
  }

  setDates(): void {
    const today = new Date();
    // Set toDate to today's date
    this.toDate = this.formatDate(today);
    const lastMonthDate = new Date(today);
    lastMonthDate.setMonth(lastMonthDate.getMonth() - 1);
    if (lastMonthDate.getMonth() !== (today.getMonth() - 1 + 12) % 12) {
      lastMonthDate.setDate(0); // Set to the last day of the previous month
    }
    this.fromDate = this.formatDate(lastMonthDate);
    this.getPopularBooks();
  }

  setupChartOptions() {
    this.basicOptions = {
      responsive: true,
      maintainAspectRatio: false,
      plugins: {
        legend: {
          display: true,
          position: 'top',
        },
      },
      scales: {
        x: {
          display: true,
          title: {
            display: true,
            text: 'Months',
          },
        },
        y: {
          display: true,
          title: {
            display: true,
            text: 'Count',
          },
        },
      },
    };
  }

  onTimeframeChange(event: any) {
    this.selectedButton = event.target.value;
    this.getUserChartData();
  }
  onframeChange(event: any) {
    this.selectedSector = event.target.value;
    this.getUserChartData();
  }

  getUserChartData() {
    this.ngxSpinnerService.show('globalSpinner');
    const type = {
      functionType: this.selectedButton,
      months: this.selectedSector,
    };
    this.dataTransferService.getChartData(type).subscribe(
      (res: any) => {
        console.log(res, 'chart Response');
        const data = res?.[0];
        this.ngxSpinnerService.hide('globalSpinner');
        this.totalUsers = data?.totalUsers || 0;
        this.totalBookClubs = data?.totalActiveBookClubs || 0;
        this.totalBooks = data?.totalBooks || 0;

        if (this.selectedButton === 'users') {
          if (this.selectedSector === '3 months') {
            const quarterlyLabels = Object.keys(data.quarterlyUserCounts);
            const quarterlyData = Object.values(data.quarterlyUserCounts);

            this.basicData = {
              labels: quarterlyLabels,
              datasets: [
                {
                  label: 'Users (Quarterly)',
                  data: quarterlyData,
                  fill: false,
                  borderColor: '#7C9C9A',
                  tension: 0.4,
                },
              ],
            };
          } else if (this.selectedSector === 'all') {
            this.basicData = {
              labels: data.monthlyUserCounts.map(
                (item: { month: any }) => item.month
              ),
              datasets: [
                {
                  label: 'Users (Monthly)',
                  data: data.monthlyUserCounts.map(
                    (item: { count: any }) => item.count
                  ),
                  fill: false,
                  borderColor: '#7C9C9A',
                  tension: 0.4,
                },
              ],
            };
          }
        } else if (this.selectedButton === 'bookClubs') {
          if (this.selectedSector === '3 months') {
            const quarterlyLabels = Object.keys(data.quarterlyBookClubCounts);
            const quarterlyData = Object.values(data.quarterlyBookClubCounts);

            this.basicData = {
              labels: quarterlyLabels,
              datasets: [
                {
                  label: 'Bookclubs (Quarterly)',
                  data: quarterlyData,
                  fill: false,
                  borderColor: '#7C9C9A',
                  tension: 0.4,
                },
              ],
            };
          } else if (this.selectedSector === 'all') {
            this.basicData = {
              labels: data.monthlyBookClubCounts.map(
                (item: { month: any }) => item.month
              ),
              datasets: [
                {
                  label: 'Bookclubs (Monthly)',
                  data: data.monthlyBookClubCounts.map(
                    (item: { count: any }) => item.count
                  ),
                  fill: false,
                  borderColor: '#7C9C9A',
                  tension: 0.4,
                },
              ],
            };
          }
        }
      },
      (error: any) => {
        this.ngxSpinnerService.hide('globalSpinner');
        console.log('error', error);
      }
    );
  }

  onSectorChange(event: any): void {
    this.selectedSector = event.target.value;
    // this.getAllInsightChartData(this.selectedTimeframe, this.selectedSector);
  }

  getTop5Records() {
    this.ngxSpinnerService.show('globalSpinner');
    this.dataTransferService.getTop5Records().subscribe({
      next: (value: any) => {
        // if (value.data) {
          this.toBeReadData = value.toBeRead;
          this.currentlyReadingData=value.currentlyReading;
        // }
        this.ngxSpinnerService.hide('globalSpinner');
      },
      error: (err) => {
        this.toastr.error(err.error.message);
        console.log(err);
        this.ngxSpinnerService.hide('globalSpinner');
      },
      complete: () => {
        this.ngxSpinnerService.hide('globalSpinner');
      },
    });
  }

  callPopularBooks() {
    if (this.fromDate) {
      if (this.toDate && this.fromDate) {
        this.getPopularBooks();
      }
    }
  }

  getPopularBooks() {
    const formattedFromDate =
      typeof this.fromDate === 'object'
        ? this.formatDate(this.fromDate)
        : this.fromDate;
    const formattedToDate =
      typeof this.toDate === 'object'
        ? this.formatDate(this.toDate)
        : this.toDate;

    if (new Date(formattedToDate) < new Date(formattedFromDate)) {
      this.toastr.error('To date must be greater than or equal to From date.');
      return;
    }

    const payload = {
      fromDate: formattedFromDate,
      toDate: formattedToDate,
    };

    this.ngxSpinnerService.show('globalSpinner');

    this.dataTransferService.getPopularBooks(payload).subscribe({
      next: (value: any) => {
        if (value.data) {
          this.popularsBooks = value.data;
        }
        this.ngxSpinnerService.hide('globalSpinner');
      },
      error: (err) => {
        this.toastr.error(err.error.message);
        console.error(err);
        this.ngxSpinnerService.hide('globalSpinner');
      },
      complete: () => {
        this.ngxSpinnerService.hide('globalSpinner');
      },
    });
  }

  formatDate(date: Date): string {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  }
}
