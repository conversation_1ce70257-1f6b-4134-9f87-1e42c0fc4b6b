<ul class="breadcrumb items-center space-x-1">
  <li *ngFor="let module of breadCrumbModules; let i = index" class="flex items-center">
    <!-- Render link for non-active items -->
    <ng-container *ngIf="!module.isActive; else activeBreadcrumb">
      <a [routerLink]="module.path" [state]="module.state" [ngClass]="inActiveColor + ' hover:underline'">
        {{ module.label }}
      </a>
    </ng-container>

    <!-- Render label for active item -->
    <ng-template #activeBreadcrumb>
      <span [ngClass]="activeColor">{{ module.label }}</span>
    </ng-template>

    <!-- Separator between items -->
    <span *ngIf="i < breadCrumbModules.length - 1" class="mx-2">/</span>
  </li>
</ul>