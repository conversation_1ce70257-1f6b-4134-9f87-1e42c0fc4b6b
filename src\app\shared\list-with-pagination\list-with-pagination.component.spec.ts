import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ListWithPaginationComponent } from './list-with-pagination.component';

describe('ListWithPaginationComponent', () => {
  let component: ListWithPaginationComponent;
  let fixture: ComponentFixture<ListWithPaginationComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ListWithPaginationComponent ]
    })
    .compileComponents();
  });

  beforeEach(() => {
    fixture = TestBed.createComponent(ListWithPaginationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
