import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { BehaviorSubject } from 'rxjs';
import { environment } from '../../../environments/environment';
import { Router } from '@angular/router';

@Injectable({
  providedIn: 'root',
})
export class DataTransferService {
  private rolesSubject = new BehaviorSubject<any>(this.getStoredRoles());
  constructor(private http: HttpClient, private router: Router) {}

  loginUser(postData: any) {
    return this.http.post(`${environment.apiBaseUrl}/auth/signin`, postData);
  }

  getAllUsers(payload: any) {
    return this.http.get(`${environment.apiBaseUrl}/users/list`, {
      params: {
        search: payload.search,
        limit: payload.limit,
        offset: payload.offset,
      },
    });
  }

  lockUnlockUser(payload: any) {
    return this.http.post(`${environment.apiBaseUrl}/users/lock`, payload);
  }

  composeEmails(payload: any) {
    return this.http.post(
      `${environment.apiBaseUrl}/notifications/composite-email`,
      payload
    );
  }

  deleteUser(id: any) {
    return this.http.delete(
      `${environment.apiBaseUrl}/users/delete-user/${id}`
    );
  }

  public getUser(userId: number) {
    return this.http.get(`${environment.apiBaseUrl}/users/get/${userId}`);
  }

  getRolesAction(roles: any) {
    const sortedRoles = roles.sort((a: any, b: any) => a.id - b.id);
    this.setRoles(sortedRoles);
  }

  setRoles(roles: any) {
    localStorage.setItem('userRoles', JSON.stringify(roles));
    this.rolesSubject.next(roles);
  }

  private getStoredRoles() {
    const storedRoles = localStorage.getItem('userRoles');
    return storedRoles ? JSON.parse(storedRoles) : null;
  }

  getRoles() {
    return this.rolesSubject.asObservable();
  }

  getbooksList(payload: any) {
    return this.http.get(`${environment.apiBaseUrl}/books/search`, {
      params: {
        query: payload.search,
        size: payload.limit,
        page: payload.offset,
        bookAuthor: payload.bookAuthor,
        bookName: payload.bookName,
      },
    });
  }

  getBookRequests(payload: any) {
    return this.http.get(`${environment.apiBaseUrl}/questions-feedback/list`, {
      params: {
        feedbackType: payload.feedbackType,
        search: payload.search,
        size: payload.limit,
        page: payload.offset,
      },
    });
  }

  updateRequestStatus(questionOrFeedbackId: any) {
    return this.http.put(
      `${environment.apiBaseUrl}/questions-feedback/updateStatus?questionOrFeedbackId=${questionOrFeedbackId}`,
      questionOrFeedbackId
    );
  }

  // filterBook(payload: any) {
  //   return this.http.post(`${environment.apiBaseUrl}/search/getAll`, payload);
  // }

  filterBook(payload: any) {
    return this.http.post(`${environment.apiBaseUrl}/search/searchBy`, payload);
  }

  getFilterData(payload: any) {
    return this.http.get(`${environment.apiBaseUrl}/search/getDataBy`, {
      params: {
        bookIds: payload.bookIds,
        filter: payload.filter,
        userId: payload.userId,
        limit: payload.limit,
        offset: payload.offset,
      },
    });
  }
  addBook(payload: any) {
    return this.http.post(`${environment.apiBaseUrl}/books/add-book`, payload);
  }

  editBook(payload: any, id: any) {
    return this.http.put(
      `${environment.apiBaseUrl}/books/update/${id}`,
      payload
    );
  }

  deleteBook(id: any) {
    return this.http.delete(`${environment.apiBaseUrl}/books/delete/${id}`);
  }

  public getBookCase(searchFileds: any) {
    return this.http.get(`${environment.apiBaseUrl}/bookCase/list`, {
      params: {
        userId: searchFileds.userId,
        limit: searchFileds.limit,
        offset: searchFileds.offset,
      },
    });
  }
  getChartData(data: any) {
    return this.http.post(
      `${environment.apiBaseUrl}/dashboard/dashboard-details`,
      data
    );
  }

  // getCommonTopshelfBooks() {
  //   return this.http.get(
  //     `${environment.apiBaseUrl}/dashboard/common-topshelf-books`
  //   );
  // }

  getTop5Records() {
    return this.http.get(
      `${environment.apiBaseUrl}/bookCase/top-cr-and-tbr`
    );
  }

  getPopularBooks(payload: any) {
    return this.http.post(
      `${environment.apiBaseUrl}/dashboard/popular-books`,
      payload
    );
  }

  getOpenLibraryBookData(searchTerm: any) {
    return this.http.get(
      `https://openlibrary.org/search.json?q=${searchTerm}&fields=key,title,author_name,isbn`
    );
  }
}
