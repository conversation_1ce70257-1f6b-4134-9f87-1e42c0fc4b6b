<div class="container-scroller">
  <div class="container-fluid page-body-wrapper full-page-wrapper">
    <div class="content-wrapper d-flex align-items-center auth px-0  d-flex justify-content-center">

      <div  class="row login-form">

        <div class="col-lg-12 login-form-header">
          <div class="d-flex  justify-content-center align-items-center py-3">
            <img style="margin-right: 22px;" height="48px" src="./assets/icons/elJuntoIcon.svg">
            <span class="logo-title">El Junto</span>
          </div>
          </div>

          <div class="col-lg-12 login-form-body py-4">
          <form class="pt-1" [formGroup]="loginForm" (ngSubmit)="onSubmit()">
            <div class="form-group">
              <label for="email">Email </label>
              <input type="email" class="form-control form-control-lg" formControlName="userEmailId"
                placeholder="Please Enter Registered Email" (blur)="onEmailBlur()" />
              <div *ngIf="userEmailId?.invalid && showEmailError"class="alert alert-danger">
                <div *ngIf="userEmailId?.errors?.required">Email is required.</div>
                <div *ngIf="userEmailId?.errors?.email">Invalid email address.</div>
              </div>
            </div>
            <div class="form-group password-wrapper">
              <label for="pass">Password </label>
              <input type="{{ passwordHidden ? 'password' : 'text' }}" class="form-control form-control-lg"
                formControlName="userCred" placeholder="Password">
              <span class="password-toggle-btn pt-20" (click)="togglePasswordVisibility()">
                <i class="fa" [ngClass]="passwordHidden ? 'fa-eye-slash' : 'fa-eye'"></i>
              </span>
            </div>
            <div class="mt-3">
              <button type="submit" class="btn btn-block btn-primary btn-lg font-weight-medium login-form-btn">Sign
                in</button>
            </div>
          </form>
        </div>


      </div>