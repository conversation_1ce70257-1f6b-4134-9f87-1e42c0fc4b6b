import { Component, Input, OnInit } from '@angular/core';
import { Router, NavigationEnd, IsActiveMatchOptions } from '@angular/router';
import { DataTransferService } from '../services/data-transfer.service';
import { NgxSpinnerService } from 'ngx-spinner';

@Component({
  selector: 'app-sidebar',
  templateUrl: './sidebar.component.html',
  styleUrls: ['./sidebar.component.scss'],
})
export class SidebarComponent implements OnInit {
  @Input() menuTitle: string;
  userRole: number = 0;
  accessMenuList: any = [];
  actionLinks: any = [];
  active: any;
  userTypeId: any;
  defaultLogoutIcon = './assets/icons/Logout.png';
  hoverLogoutIcon = './assets/icons/Logout Tan.png';
  logoutIcon = this.defaultLogoutIcon;
  constructor(
    private dataTransferService: DataTransferService,
    private router: Router,
    private ngxSpinnerService: NgxSpinnerService
  ) {

    this.router.events.subscribe((event) => {
      if (event instanceof NavigationEnd) {
        this.resetSidebar();
      }
    });

  }


  toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    if (sidebar) {
      sidebar.classList.toggle('active');
    }
  }

  resetSidebar() {
    const sidebar = document.getElementById('sidebar');
    if (sidebar && sidebar.classList.contains('active')) {
      sidebar.classList.remove('active');
    }
  }

  isMenuActive(route: string): boolean {
    const matchOptions: IsActiveMatchOptions = {
      paths: 'subset', 
      queryParams: 'ignored', 
      matrixParams: 'ignored', 
      fragment: 'ignored' 
    };

    return this.router.isActive(route, matchOptions);
  }

  getActiveIcon(defaultIcon: string): string {
    return defaultIcon.replace('.svg', '_Tan.svg');
  }

  isTouchDevice(): boolean {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0;
  }

  getIcon(menu: any): string {
    if (this.isTouchDevice()) {
      return this.isMenuActive(menu.route) ? this.getActiveIcon(menu.icon) : menu.icon;
    } else {
      return menu.isHovered || this.isMenuActive(menu.route) ? this.getActiveIcon(menu.icon) : menu.icon;
    }
  }


  onHover(menu: any): void {
    if (!this.isTouchDevice()) {
      menu.isHovered = true;
    }
  }

  onMouseOut(menu: any): void {
    if (!this.isTouchDevice()) {
      menu.isHovered = false;
    }
  }


  ngOnInit(): void {
    this.getRoles();

  }
  logout() {
    this.ngxSpinnerService.show('globalSpinner');
    localStorage.clear();
    this.router.navigate(['']);
    this.ngxSpinnerService.hide('globalSpinner');
  }

  getRoles() {
    this.dataTransferService.getRoles().subscribe((res: any) => {
      res.forEach((element: any) => {
        if (element.menu == true) {
          this.accessMenuList.push(element);
        }
      });
    })
  }

  showModal() {
    const modal = document.getElementById('logOutModal');
    if (modal != null) {
      modal.style.display = 'block';
    }
  }

  hideModal() {
    const modal = document.getElementById('logOutModal');
    if (modal != null) {
      modal.style.display = 'none';
    }
  }

 

  onLogOutHover() {
    this.logoutIcon = this.hoverLogoutIcon;
  }

  onLoOutLeave() {
    this.logoutIcon = this.defaultLogoutIcon;
  }
}
