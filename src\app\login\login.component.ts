import { Component, OnInit } from '@angular/core';
import { FormGroup, FormBuilder, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import * as CryptoJS from 'crypto-js';
import { ToastrService } from 'ngx-toastr';
import { DataTransferService } from '../shared/services/data-transfer.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { Constants } from '../config/constants';
@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.scss']
})
export class LoginComponent implements OnInit {
  loginForm: FormGroup;
  submitted = false;
  passwordHidden: boolean = true;
  private jwtSecret = Constants.JwtSecretKey;
  showEmailError = false;

  constructor(private formBuilder: FormBuilder,
    private toastr: ToastrService,
    private router: Router,
    private dataTransferService: DataTransferService,
    private ngxSpinnerService: NgxSpinnerService) { }
  get f() {
    return this.loginForm.controls;
  }
  ngOnInit(): void {
    let token = localStorage.getItem('token');
    if (token) {
      this.router.navigate([`/dashboard`]);
    }
    else {
      this.loginForm = this.formBuilder.group({
        userEmailId: ['', [Validators.required, Validators.email]],
        userCred: ['', [Validators.required]]
      });
    }

    this.loginForm.controls['userEmailId'].statusChanges.subscribe((status) => {
      if (status === 'INVALID' && this.loginForm.controls['userEmailId'].touched) {
        this.showEmailError = true;
      } else {
        this.showEmailError = false;
      }
    });
  }

  onEmailBlur(): void {
    this.showEmailError = this.loginForm.controls['userEmailId'].invalid;
  }

  get userEmailId() {
    return this.loginForm.get('userEmailId');
  }

  togglePasswordVisibility() {
    this.passwordHidden = !this.passwordHidden;
  }

  hashPassword(password: string): string {
    const keyBytes = CryptoJS.enc.Base64.parse(this.jwtSecret);

    const hashedPassword = CryptoJS.SHA256(password, keyBytes).toString(CryptoJS.enc.Hex);

    return hashedPassword;
  }

  onSubmit() {
    try {
      this.submitted = true;
      if (this.loginForm.invalid) {
        this.toastr.error("Please input correct values");
        this.ngxSpinnerService.hide('globalSpinner');
        return;
      }
      this.ngxSpinnerService.show('globalSpinner');

      const rawPassword = this.loginForm.controls['userCred'].value;
      const hashedPassword = this.hashPassword(rawPassword);
      const postData = {
        userEmailId: this.loginForm.get('userEmailId')?.value,
        userCred: hashedPassword
      };

      this.dataTransferService.loginUser(postData).subscribe({
        next: (res: any) => {
          if (res) {
            this.toastr.success("User successfully logged in");
            localStorage.setItem('token', res.token);
            localStorage.setItem('userId', res.userId);
            this.dataTransferService.getRolesAction(res.roleActions);
            this.router.navigate([`/dashboard`]);
          } else {
            this.toastr.error("Login failed. Please try again.");
          }
          this.ngxSpinnerService.hide('globalSpinner');
        },
        error: (error) => {
          this.ngxSpinnerService.hide('globalSpinner');
          this.toastr.error("Invalid Credentials");
          this.router.navigate([`/login`]);
          console.log(error);
        }
      });
    } catch (err) {
      console.log(err);
    }
  }
}
