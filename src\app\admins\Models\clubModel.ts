export class BookClub {
    bookClubId: number;
    bookClubType: string;
    userId: number;
    totalPositions: number;
    totalMembers: number;
    totalVacancies: number;
    clubCreatedDate: number; // or Date
    clubCharter: string;
    memberReqPrompt: string;
    bookId: number;
    bookClubName: string;
    clubStatus: string;
    bookName: string;
    bookAuthor: string;
    clubCount: string;
    incomingRequest: boolean;

    constructor(bookClub?: Partial<BookClub>) {
        this.bookClubId = bookClub?.bookClubId ?? 0;
        this.bookClubType = bookClub?.bookClubType ?? '';
        this.userId = bookClub?.userId ?? 0;
        this.totalPositions = bookClub?.totalPositions ?? 0;
        this.totalMembers = bookClub?.totalMembers ?? 0;
        this.totalVacancies = bookClub?.totalVacancies ?? 0;
        this.clubCreatedDate = bookClub?.clubCreatedDate ?? Date.now();
        this.clubCharter = bookClub?.clubCharter ?? '';
        this.memberReqPrompt = bookClub?.memberReqPrompt ?? '';
        this.bookId = bookClub?.bookId ?? 0;
        this.bookClubName = bookClub?.bookClubName ?? '';
        this.clubStatus = bookClub?.clubStatus ?? 'Active';
        this.bookName = bookClub?.bookName ?? '';
        this.bookAuthor = bookClub?.bookAuthor ?? '';
        this.clubCount = bookClub?.clubCount ?? '';
        this.incomingRequest = bookClub?.incomingRequest ?? false;
    }
}
