import { Component, OnInit, ViewChild } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { DataTransferService } from 'src/app/shared/services/data-transfer.service';
import { FormGroup, FormBuilder, Validators, FormControl } from '@angular/forms';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';

import { NgxSpinnerService } from 'ngx-spinner';
import { moduleTypes,Constants } from 'src/app/config/constants';
import { getBreadCrumbModules } from 'src/app/config/commonHelper';
import { ListWithPaginationComponent } from 'src/app/shared/list-with-pagination/list-with-pagination.component';
@Component({
  selector: 'app-users',
  templateUrl: './users.component.html',
  styleUrls: ['./users.component.scss']
})
export class UsersComponent implements OnInit {
  @ViewChild(ListWithPaginationComponent) listComponent!: ListWithPaginationComponent;
  usersList: any;
  AddUser = false;
  showForm = false;
  roles: any;
  totalNumberOfRecords: number = 0;
  offset: number = 1;
  data: any[] = [];
  columns: any[] = [
    { title: "Profile Picture", dataKey: "userProfilePicture" },
    { title: "Name", dataKey: "userName" },
    { title: "Email", dataKey: "userEmailId" },
    { title: "Unlock/Lock", dataKey: "userIsLocked" },
    // { title: "Action", dataKey: "action" },


  ]
  isLargeScreen: boolean = true;
  term: string;
  isReadonly = false;
  userForm: FormGroup;
  submitted = false;
  title = 'Add New';
  queryParam: any;
  limit: number = 10;
  menuTitle: string = 'List Of Users';
  searchTerm = '';  
  searchControl: FormControl = new FormControl();
  _tomodule: string = moduleTypes.USERPROFILE;
  breadCrumbModules = getBreadCrumbModules(moduleTypes.USER);
  compileEmailForm:FormGroup;

  constructor(private formBuilder: FormBuilder,
    private dataTransferService: DataTransferService,
    private toastr: ToastrService,
    private ngxSpinnerService: NgxSpinnerService,
  ) { 

    this.compileEmailForm = this.formBuilder.group({
      subject: new FormControl('', [Validators.required]),
      body: new FormControl('', [Validators.required])
    });

  }

  get f() {
    return this.compileEmailForm.controls;
  }

  ngOnInit(): void {
    this.getAllUsers();
    this.checkScreenSize();
  }

  private checkScreenSize() {
    this.isLargeScreen = window.innerWidth > 1100;
  }

  onSearch(searchValue: string) {
    this.offset = 0;  
    this.searchTerm = searchValue;  
    console.log("searchValue",searchValue);
    
    this.getAllUsers();
  }

  getAllUsers() {
    this.ngxSpinnerService.show("globalSpinner");
    const payload = {
      search: this.searchTerm,
      limit: this.limit,
      offset: this.offset-1
    }
    this.dataTransferService.getAllUsers(payload).subscribe({
      next: (value: any) => {
        // this.data = value;
        if (value.Data) {
          this.data = value.Data;
          this.totalNumberOfRecords = value.count;

        }
        this.ngxSpinnerService.hide('globalSpinner');
      }, error: (err) => {
        this.toastr.error(err.error.message);
        console.log(err);
        this.ngxSpinnerService.hide('globalSpinner');
      }, complete: () => {
        this.ngxSpinnerService.hide("globalSpinner");
      }
    });
  }



  onCurrentPageChanged = (currentOffset: number) => {
    this.offset = currentOffset;
    this.getAllUsers();
  }

  onPageSizeChanged = (pageSizeChanged: number) => {
    this.offset=1;
    this.limit = pageSizeChanged;
    this.getAllUsers();
  }

  deleteRecord = (elementID: number) => {
    this.ngxSpinnerService.show('globalSpinner');
    this.dataTransferService.deleteUser(elementID).subscribe({
      next: (response: any) => {
        if (response.statusCode === 200) {
          this.toastr.success(response.message);
          this.searchTerm='';
          this.limit=10;
          this.offset=0;
          this.getAllUsers();
          this.hideModalInPagination('deleteRecordModal');
        }
        this.ngxSpinnerService.hide('globalSpinner');
      },
      error: (err) => {
        this.toastr.error(err.error.message || 'An error occurred while deleting the user');
        console.log(err);
        this.hideModalInPagination('deleteRecordModal');
        this.ngxSpinnerService.hide('globalSpinner');
      },
      complete: () => {
        this.ngxSpinnerService.hide("globalSpinner");
        this.hideModalInPagination('deleteRecordModal');
      }
    });
  }


  hideModalInPagination(modalId: string) {
    if (this.listComponent) {
      this.listComponent.hideModal(modalId);
    }
  }

  lockUnlockUser = (user: any) => {
    const payload = {
      userId: user.userId,
      userIsLocked: !user.userIsLocked
    }

    this.ngxSpinnerService.show('globalSpinner');
    this.dataTransferService.lockUnlockUser(payload).subscribe({
      next: (response: any) => {
        if (response.statusCode === 200) {
          this.getAllUsers();
          this.hideModalInPagination('lockUnlockModal');
          this.toastr.success(response.message);

          console.log(`user updated successfully with ID: ${user.userId}`);
        }
        this.ngxSpinnerService.hide('globalSpinner');
      },
      error: (err) => {
        this.toastr.error(err.error.message || 'An error occurred while updating the user');
        console.log(err);
        this.hideModalInPagination('lockUnlockModal');
        this.ngxSpinnerService.hide('globalSpinner');
      },
      complete: () => {
        this.ngxSpinnerService.hide("globalSpinner");
        this.hideModalInPagination('lockUnlockModal');
      }
    });
  }

  showModal(modalId: string, bookData?: any) {
    const modal = document.getElementById(modalId);
    if (modal != null) {
      modal.style.display = 'block';
    }
  }
hideModal(modalId: string) {
    const modal = document.getElementById(modalId);
    if (modal != null) {
      modal.style.display = 'none';
    }

    if(modalId==='compileEmailModal'){
      this.compileEmailForm.reset();
    }
  }

  composeEmails(){
    if(this.compileEmailForm.valid){
    this.ngxSpinnerService.show('globalSpinner');
    const formValues = this.compileEmailForm.value;
    const formattedBody = formValues.body.replace(/\n/g, '<br>');
    const payload = {
      subject: formValues.subject,
      body: formattedBody,
    };
    console.log('Email Data:', payload);
    this.dataTransferService.composeEmails(payload).subscribe({
      next: (response: any) => {
        if (response.statusCode === 200) {
          this.hideModal('compileEmailModal');
          this.toastr.success(response.message);
        }
        this.ngxSpinnerService.hide('globalSpinner');
      },
      error: (err) => {
        this.toastr.error(err.error.message || 'An error occurred while sending the emails');
        console.log(err);
        this.ngxSpinnerService.hide('globalSpinner');
      },
      complete: () => {
        this.ngxSpinnerService.hide("globalSpinner");
        this.hideModal('compileEmailModal');
      }
    });
  }else{
    this.toastr.error("Please fill all required fields correctly");
  }
  
  }
}
