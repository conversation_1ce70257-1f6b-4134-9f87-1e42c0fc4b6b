import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup } from '@angular/forms';
import { getBreadCrumbModules } from 'src/app/config/commonHelper';
import { moduleTypes } from 'src/app/config/constants';
interface RouteParam {
  id: number;
  view: boolean;
  module: string;
}
@Component({
  selector: 'app-reports',
  templateUrl: './reports.component.html',
  styleUrls: ['./reports.component.scss']
})

export class ReportsComponent implements OnInit {
  params: RouteParam;
  searchForm: FormGroup;
  dropdownOptions1 = [
    { value: 'Top Shelf', label: 'Top Shelf' },
    { value: 'Most Popular', label: 'Most Popular' },
    { value: "Who's read this?" , label: "Who's read this?" },
    { value: "Who's reading this?" , label: "Who's reading this?" },
    { value: "What clubs are reading this?" , label: "What clubs are reading this?" },

  ];

  totalNumberOfRecords: number = 0;
  offset: number = 0;
  data: any[] = [];
  columns: any[] = [
    { title: "Name of the book", dataKey: "bookName" },
    { title: "Author", dataKey: "bookAuthor" },
    // { title: "Profile", dataKey: "profile" },
    // { title: "Lock/Unlock", dataKey: "lock/unlock" },
    // { title: "Action", dataKey: "action" },


  ]
  limit: number = 10;

  constructor(private fb : FormBuilder) { }
  breadCrumbModules: { label: string, path: string, isActive?: boolean }[] = [];
  
  ngOnInit(): void {
    this.searchForm = this.fb.group({
      option1: [''],  
      option2: [''],
      input1 : ['']
    });
    this.params = history.state;
    let module = this.params?.module ?? moduleTypes.REPORTS;
    if (module) {
      this.breadCrumbModules = getBreadCrumbModules(module);
    }
  }
  onSubmit(){
    console.log('Form values:', this.searchForm.value);
  }

  onCurrentPageChanged = (currentOffset: number) => {
    this.offset = currentOffset - 1;  
  };

  onPageSizeChanged = (pageSizeChanged: number) => {
    this.offset=0;
    this.limit = pageSizeChanged;
  };

  

}
