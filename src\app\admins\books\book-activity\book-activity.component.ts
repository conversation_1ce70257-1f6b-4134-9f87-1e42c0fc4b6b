import { Component, OnInit, ViewChild, HostListener } from '@angular/core';
import { ListWithPaginationComponent } from 'src/app/shared/list-with-pagination/list-with-pagination.component';
import { ToastrService } from 'ngx-toastr';
import { DomSanitizer } from '@angular/platform-browser';
import { Router, ActivatedRoute } from '@angular/router';
import { DataTransferService } from 'src/app/shared/services/data-transfer.service';
import {
  FormGroup,
  FormBuilder,
  Validators,
  FormControl,
} from '@angular/forms';
import { NgxSpinnerService } from 'ngx-spinner';
import { debounceTime, distinctUntilChanged, finalize } from 'rxjs/operators';
import { getBreadCrumbModules } from 'src/app/config/commonHelper';
import { moduleTypes } from 'src/app/config/constants';
import * as XLSX from 'xlsx';
import { ChangeDetectorRef } from '@angular/core';
import { DatePipe } from '@angular/common';
@Component({
  selector: 'app-book-activity',
  templateUrl: './book-activity.component.html',
  styleUrls: ['./book-activity.component.scss'],
})
export class BookActivityComponent implements OnInit {
  @ViewChild(ListWithPaginationComponent)
  listComponent!: ListWithPaginationComponent;
  totalNumberOfRecords: any = 0;
  offset: number = 1;
  params: any;
  data: any[] = [];
  columns: any[] = [];
  limit: number = 10;
  menuTitle: string = 'List Of Books';
  searchControl: FormControl = new FormControl();
  searchTerm = '';
  breadCrumbModules: any;
  bookId: any;
  _tomodule: string = moduleTypes.BOOKACTIVITY;
  activeTab: string;
  toDownloadExcelData: any;
  filterWhoReadData: any[] = [];
  filterWhoReadingData: any[] = [];
  filterReadingClubs: any[] = [];
  previousSearchTerm: string = '';
  showStaticPagination: boolean = true;
  isLargeScreen: boolean = true;
  bookOrClubName: any;
  filtersData: any[] = [];
  filtersBook: any[] = [
    { name: 'People reading this book', value: 'whoReadingThis' },
    { name: 'People that have read this book', value: 'whoReadThis' },
    { name: 'People that want to read this book', value: 'Interested' },
    { name: 'Clubs reading this book', value: 'whatClubIntoThis' },
  ];
  filtersAuthor: any[] = [
    { name: 'People reading this author', value: 'whoReadingThis' },
    { name: 'People that have read this author', value: 'whoReadThis' },
    { name: 'People that want to read this author', value: 'Interested' },
    { name: 'Clubs reading this author', value: 'whatClubIntoThis' },
  ];
  filterType: string = 'whoReadingThis';
  constructor(
    private dataTransferService: DataTransferService,
    private toastr: ToastrService,
    private ngxSpinnerService: NgxSpinnerService,
    private router: Router,
    private datePipe: DatePipe
  ) {}

  ngOnInit(): void {
    this.checkScreenSize();
    this.params = history.state;
    console.log('params', this.params);

    if (this.params.activeTab) {
      localStorage.setItem('bookActivityParams', JSON.stringify(this.params));
      this.setParamsData();
    } else {
      this.params = JSON.parse(
        localStorage.getItem('bookActivityParams') || '{}'
      );
      this.setParamsData();
      console.log('params', this.params);
    }
    this.getFilterData();
  }

  setParamsData() {
    this.activeTab = this.params.activeTab;
    console.log('this.activeTab', this.activeTab);
    this.searchTerm = this.params.searchTerm;
    this.bookOrClubName =
      this.activeTab === 'whoIntoThisBook'
        ? this.params.element.book_names
        : this.params.element.book_author;

    this.filtersData =
      this.activeTab === 'whoIntoThisBook'
        ? this.filtersBook
        : this.filtersAuthor;

    const state: any = {
      activeTab: this.activeTab,
      searchTerm: this.searchTerm,
      data: this.params.element,
    };
    this.breadCrumbModules = getBreadCrumbModules(
      moduleTypes.BOOKACTIVITY,
      state
    );

    // if (this.activeTab === 'Who’s Read This?') {
    //   this.data = this.params.element.users;
    //   this.columns = [
    //     { title: 'Name', dataKey: 'userName' },
    //     { title: 'Email', dataKey: 'userEmailId' },
    //     { title: 'Ratings', dataKey: 'bookRatings' },
    //     { title: 'Review', dataKey: 'bookReview' },
    //   ];
    //   this.cdr.detectChanges();
    // } else if (this.activeTab === 'Who’s Reading This?') {
    //   this.data = this.params.element.users;
    //   this.columns = [
    //     { title: 'Name', dataKey: 'userName' },
    //     { title: 'Email', dataKey: 'userEmailId' },
    //   ];
    //   this.cdr.detectChanges();
    // } else if (this.activeTab === 'What Clubs Are Reading This?') {
    //   this.data = this.params.element.bookClubs;
    //   this.columns = [
    //     { title: 'Club Name', dataKey: 'bookClubName' },
    //     { title: 'Club Type', dataKey: 'bookClubType' },
    //   ];
    //   this.cdr.detectChanges();
    // }
  }

  private checkScreenSize() {
    this.isLargeScreen = window.innerWidth > 1100;
  }

  onSearch(searchValue: string) {
    this.offset = 1;
    this.searchTerm = searchValue;
    console.log('searchValue', searchValue);
    // this.getAllUsers();
  }

  downloadReport() {
    let fileName = 'Data';

    if (!this?.data || this.data?.length === 0) {
      console.warn('No data to export');
      return;
    }

    this.toDownloadExcelData = [];

    if (
      this.filterType === 'whoReadingThis' ||
      this.filterType === 'Interested'
    ) {
      this.toDownloadExcelData = this.data.map((item) => ({
        Name: item?.userName || 'N/A',
        Email: item?.userEmailId || 'N/A',
      }));
    } else if (this.filterType === 'whoReadThis') {
      this.toDownloadExcelData = this.data.map((item) => ({
        Name: item?.userName || 'N/A',
        Email: item?.userEmailId || 'N/A',
        ReadingCompleteDate: item?.readingCompleteDate
          ? this.datePipe?.transform(item.readingCompleteDate, 'dd MMM yyyy')
          : 'N/A',
        Ratings: item?.ratings ?? 'N/A',
        Review: item?.review ?? 'N/A',
      }));
    } else if (this.filterType === 'whatClubIntoThis') {
      this.toDownloadExcelData = this.data.map((item) => ({
        Name: item?.bookClubName || 'N/A',
        Type: item?.bookClubType || 'N/A',
      }));
    }

    if (!this.toDownloadExcelData || this.toDownloadExcelData.length === 0) {
      console.warn('No processed data to export');
      this.toastr.error('No validdata to export');
      return;
    }

    try {
      // Convert data to a worksheet
      const worksheet: XLSX.WorkSheet = XLSX.utils.json_to_sheet(
        this.toDownloadExcelData
      );

      // Create a new workbook and append the worksheet
      const workbook: XLSX.WorkBook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'BookReviews');

      // Generate the Excel file and trigger download
      XLSX.writeFile(workbook, `${fileName}.xlsx`);
    } catch (error) {
      console.error('Error generating Excel file:', error);
    }
  }

  onPageSizeChanged = (pageSizeChanged: number) => {
    this.limit = pageSizeChanged;
    this.getFilterData();
  };

  goBack() {
    this.router.navigate(['/admins/book-list'], {
      state: {
        activeTab: this.activeTab,
        searchTerm: this.searchTerm,
        data: this.params.element,
      },
    });
  }

  @HostListener('window:popstate', ['$event'])
  onPopState(event: PopStateEvent) {
    this.goBack();
  }

  getFilterData() {
    const payload = {
      bookIds: this.params.element.book_id || this.params.element.book_ids,
      filter: this.filterType,
      userId: parseInt(localStorage.getItem('userId') || '0', 10),
      limit: this.limit,
      offset: this.offset - 1,
    };

    this.ngxSpinnerService.show('globalSpinner');

    const columnConfig: {
      [key: string]: { title: string; dataKey: string }[];
    } = {
      whoReadingThis: [
        { title: 'Profile Picture', dataKey: 'userProfilePicture' },
        { title: 'Name', dataKey: 'userName' },
        { title: 'Email', dataKey: 'userEmailId' },
      ],
      whoReadThis: [
        { title: 'Profile Picture', dataKey: 'userProfilePicture' },
        { title: 'Name', dataKey: 'userName' },
        { title: 'Email', dataKey: 'userEmailId' },
        { title: 'Completion Date', dataKey: 'readingCompleteDate' },
        { title: 'Ratings', dataKey: 'ratings' },
        { title: 'Review', dataKey: 'review' },
      ],
      Interested: [
        { title: 'Profile Picture', dataKey: 'userProfilePicture' },
        { title: 'Name', dataKey: 'userName' },
        { title: 'Email', dataKey: 'userEmailId' },
      ],
      whatClubIntoThis: [
        { title: 'Name', dataKey: 'book_club_name' },
        { title: 'Type', dataKey: 'book_club_type' },
      ],
    };

    this.dataTransferService
      .getFilterData(payload)
      .pipe(finalize(() => this.ngxSpinnerService.hide('globalSpinner')))
      .subscribe({
        next: (value: any) => {
          if (!value?.data) {
            this.data = [];
            return;
          }

          this.columns = columnConfig[this.filterType] || [];

          if (this.filterType === 'whoReadThis') {
            this.data = value.data.map((item: any) => ({
              ...item,
              readingCompleteDate: item?.readingCompleteDate
                ? this.datePipe?.transform(
                    item.readingCompleteDate,
                    'dd MMM yyyy'
                  )
                : 'N/A',
            }));
          } else {
            this.data = value.data;
          }

          this.totalNumberOfRecords = value?.count || 0;
        },
        error: (err) => {
          this.toastr.error(err?.error?.message || 'Something went wrong');
          console.error(err);
        },
      });
  }
}
