import { Component, forwardRef, Input, OnInit } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
@Component({
  selector: 'app-input-wrapper',
  templateUrl: './input-wrapper.component.html',
  styleUrls: ['./input-wrapper.component.scss'],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => InputWrapperComponent),
      multi: true
    }
  ]
})
export class InputWrapperComponent implements OnInit {

  @Input() id: string;
  @Input() label: string;
  @Input() type: string;
  @Input() set isErrored(is) {
    this._isErrored = is;
  };
  @Input() set errorMessage(value) { this._errorMessage = value }
  @Input() isReadonly: boolean;
  @Input() placeholder: string;
  @Input() isRequired: boolean;
  @Input() booleanDisplayText = false;
  private _value: any;
  private _errorMessage: any;
  private _isErrored: any;
  get errorMessage(): any {
    return this._errorMessage;
  }
  get isErrored(): any {
    return this._isErrored;
  }
  set value(value: any) {
    if (this.booleanDisplayText && typeof value === 'boolean') {
      this._value = value ? 'Yes' : 'No';
    } else {
      this._value = value;
    }
  }

  get value(): any {
    return this._value;
  }

  onChange = (event: any) => {

  };

  onValueChange(event: any) {
    this.value = event.target.value;
    this.onChange(this.value);       // Notify the form control about the change


  };
  onTouched = () => { };

  writeValue(value: any) {
    this.value = value;
  }

  registerOnChange(fn: any) {
    this.onChange = fn;
  }

  registerOnTouched(fn: any) {
    this.onTouched = fn;
  }

  ngOnInit(): void {
  }

  
}
