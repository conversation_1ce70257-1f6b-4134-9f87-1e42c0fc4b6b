<div class="container-scroller">

  <nav class="navbar col-lg-12 col-12 p-0 fixed-top d-flex flex-row">

    <div class="navbar-brand-wrapper d-flex align-items-center">

       <!-- Brand logo for larger screens -->
       <a class="navbar-brand brand-logo">
        <img class="img-logo" height="48" width="48" src="./assets/icons/elJuntoIcon.svg" alt="ELJUNTO logo" /><span class="logo-title">El
          Junto</span>
      </a>

      <!-- Brand logo for small screens -->
      <a class="navbar-brand ml-2 brand-logo-mini">
        <img class="img-logo" src="./assets/icons/elJuntoIcon.svg" alt="ELJUNTO logo" /><span class="logo-title">El
          Junto</span>
      </a>
     
    </div>

    <div class="navbar-menu-wrapper d-flex align-items-center"><span class="menuTitle ">{{menuTitle}}</span>
      <button class="navbar-toggler navbar-toggler-right d-lg-none align-self-center" type="button"
        (click)="toggleSidebar()">
        <span class="icon-menu"></span>
      </button>
    </div>
  </nav>

  <div class="container-fluid page-body-wrapper">
    <nav class="sidebar sidebar-offcanvas" id="sidebar">
      <ul class="nav">
        <li class="nav-item" *ngFor="let menu of accessMenuList">
          <a class="nav-link" [routerLink]="menu.route" [ngClass]="{'active-menu': isMenuActive(menu.route)}"
            (mouseover)="onHover(menu)" (mouseout)="onMouseOut(menu)">
            <img class="menu-icons" [src]="getIcon(menu)" style="height: 30px; width: 30px;" />
            <span class="menu-title" [ngStyle]="{'color': isMenuActive(menu.route) ? '#FFF5D6' : '#253943'}">
              {{menu.name}}
            </span>
          </a>
        </li>

        <li class="nav-item logout-btn">
          <a class="nav-link btn btn-outline-primary" (click)="showModal()" style="cursor: pointer;">
            <i class="fa-solid fa-arrow-right-from-bracket"></i>
            <span class="menu-title">Logout</span>
          </a>
        </li>

        <!-- <li class="nav-item logout-btn">
          <a class="nav-link btn btn-outline-primary" (mouseenter)="onLogOutHover()" 
          (mouseleave)="onLoOutLeave()">
            <img class="menu-icons"
                 [src]="logoutIcon"
                 style="height: 25px; width: 25px;" />
            <span class="menu-title">Logout </span>
          </a>
        </li>
     -->


      </ul>
    </nav>


    <div class="main-panel">
      <ng-content></ng-content>

      <footer class="footer">
        <!-- <div class="d-sm-flex justify-content-center justify-content-sm-between"> -->
        <!-- <span class="text-muted text-center text-sm-left d-block d-sm-inline-block">© 2022, Copyright reserved by <a
              href="https://www.eljunto.org.uk/" target="_blank">El Junto</a> </span>
          <span class="float-none float-sm-right d-block mt-1 mt-sm-0 text-center">Designed and Developed by <a
              href="https://prysomsystems.com/" target="_blank">Prysom Systems.</a> </span> -->
        <span class="footer-text">© 2024 by El Junto, Inc</span>
        <!-- </div> -->
      </footer>
    </div>
  </div>
</div>


<!-- Modal -->
<!-- <div id="logOutModal" class="modal logOutModal">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h3>Confirmation !</h3>
        <button type="button" (click)="hideModal()" class="close" data-bs-dismiss="modal"
        style="color: black;">&times;</button>
            </div>
      <div class="modal-body text-center">
        <i class="fa-solid fa-arrow-right-from-bracket" style="color: red;"></i>
        <h3 class="my-3"><strong>Are you sure?</strong></h3>
        <p>Do you really want to logout?</p>
      </div>
      <div class="modal-footer d-flex justify-content-center">
        <button type="button" class="btn btn-outline-primary" data-dismiss="modal" (click)="hideModal()">Cancel</button>
        <button class="btn btn-primary" (click)="logout()" data-dismiss="modal">Logout</button>
      </div>
    </div>
  </div>
</div> -->


<div class="modal" id="logOutModal">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content el-modal">
      <div class="modal-header" style="border-bottom: none;">
        <h5 class="modal-title w-100 text-center">
          Are you sure you want to logout?
        </h5>
        <button type="button" (click)="hideModal()" class="close" data-bs-dismiss="modal"
          style="color: black;">&times;</button>
      </div>
      <div class="modal-footer justify-content-center" style="border-top: none;">
        <button type="button" class="btn btn-outline-primary" (click)="hideModal()">Cancel</button>
        <button (click)="logout()" type="submit" class="btn btn-primary">Logout</button>
      </div>
    </div>
  </div>
</div>