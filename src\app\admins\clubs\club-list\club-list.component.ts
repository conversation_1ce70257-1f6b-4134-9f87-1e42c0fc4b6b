import { Component, OnInit, ViewChild } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { FormControl } from '@angular/forms';
import { NgxSpinnerService } from 'ngx-spinner';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { getBreadCrumbModules } from 'src/app/config/commonHelper';
import { moduleTypes } from 'src/app/config/constants';
import { BookClubService } from 'src/app/shared/services/book-club.service';
import { ListWithPaginationComponent } from 'src/app/shared/list-with-pagination/list-with-pagination.component';
import { DataTransferService } from 'src/app/shared/services/data-transfer.service';
@Component({
  selector: 'app-club-list',
  templateUrl: './club-list.component.html',
  styleUrls: ['./club-list.component.scss'],
})
export class ClubListComponent implements OnInit {
  @ViewChild(ListWithPaginationComponent) listComponent!: ListWithPaginationComponent;
  totalNumberOfRecords: number = 0;
  offset: number = 1;
  bookClubList: any[] = [];
  columns: any[] = [
    { title: "Name", dataKey: "bookClubName" },
    { title: "Type", dataKey: "bookClubType" },
  ]

  isLargeScreen: boolean = true;
  limit: number = 10;
  menuTitle: string = 'List Of Bookclubs';
  searchControl: FormControl = new FormControl();
  breadCrumbModules = getBreadCrumbModules(moduleTypes.CLUBS);
  _tomodule: string = moduleTypes.CLUBS;
  searchTerm = '';  

  constructor(
    private toastr: ToastrService,
    private ngxSpinnerService: NgxSpinnerService,
    private bookClubService: BookClubService,
    private dataTransferService: DataTransferService
) { }

  ngOnInit(): void {
    this.getClubList();
    this.checkScreenSize();

  }

  onSearch(searchValue: string) {
    this.offset = 1;
    this.searchTerm=searchValue;
    this.getClubList();
  }

  private checkScreenSize() {
    this.isLargeScreen = window.innerWidth > 1100;
  }

  getClubList() {
    const payload = {
      search: this.searchTerm,
      limit: this.limit,
      offset: this.offset-1,
    };
    this.ngxSpinnerService.show("globalSpinner");
    this.bookClubService.getClubs(payload).subscribe({
      next: (value: any) => {
        if (value.data) {
          this.totalNumberOfRecords = value.count;
          this.bookClubList = value?.data.map((club: any) => {
            return {
              ...club,
              bookClubType: club.bookClubType == "STANDING" ? 'Standing Book Club' : club.clubCount,
            };
          });
        }
        this.ngxSpinnerService.hide('globalSpinner');
      },
      error: (err) => {
        this.toastr.error(err.error.message);
        console.log(err);
        this.ngxSpinnerService.hide('globalSpinner');
      },
      complete: () => {
        this.ngxSpinnerService.hide('globalSpinner');
      },
    });
  }

  onCurrentPageChanged = (currentOffset: number) => {
    this.offset = currentOffset ;
    this.getClubList();
  };

  onPageSizeChanged = (pageSizeChanged: number) => {
    this.offset=1;
    this.limit = pageSizeChanged;
    this.getClubList();
  };


  deleteRecord = (elementID: number) => {
    console.log("elementID", elementID);
  }

  hideModalInPagination(modalId: string) {
    if (this.listComponent) {
      this.listComponent.hideModal(modalId);
    }
  }

}
