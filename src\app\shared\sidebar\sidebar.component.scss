.brand-logo img {
  margin: auto;
  margin-right: 22px;
}

.navbar-menu-wrapper {
  background: #7C9C9A;
  transition: width 0.25s ease;
  color: #6C7383;
  padding-right: 2.375rem;
  padding-left: 2.375rem;
  height: 60px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-bottom: 1px solid #253943;
}

.navbar .navbar-brand-wrapper {
  border-right: 1px solid #253943;
}

.menuTitle {
  font-size: 20px;
  font-weight: 700;
  line-height: 24.8px;
  color: #253943;
  text-align: center;
}

.navbar-toggler {
  margin-right: 15px;
  display: flex;
}

@media (max-width: 991px) {
  .navbar .navbar-menu-wrapper {
    width: calc(70% - 55px);
    padding-left: 0px;
    padding-right: 11px;
    justify-content: center;
  }

  .navbar .navbar-brand-wrapper {
    width: 100px;
    border-right: none;
  }

  .navbar {
    border-bottom: 1px solid #253943;
  }

  .navbar-menu-wrapper {
    border-bottom: none;
  }

  .sidebar {
    border-left: 1px solid #253943;
  }

}

@media (max-width: 768px) {
  .sidebar-offcanvas {
    width: 100%;
    position: fixed;
  }

  .container-fluid {
    padding-left: 0;
  }

  .menuTitle {
    font-size: 20px;
    font-weight: 600;
    line-height: 20px;
    text-align: left;
    width: 100%;
    padding-left: 0;
    padding-right: 0;
  }

  .navbar-menu-wrapper {
    display: none;
  }

  .navbar-toggler {
    display: block;
  }



}

@media (max-width: 480px) {
  .navbar .navbar-brand-wrapper {
    width: 100px;
    padding-left: 0.5rem;
  }

  .menuTitle {
    font-size: 14px;
    font-weight: 600;
    line-height: 18px;
  }


  .logo-title {
    font-size: 14px;
  }
}

.sidebar {
  height: 1252px;
  background: #7C9C9A;
  font-family: 'Libre Baskerville';
  font-weight: 400;
  font-size: 16px;
  padding: 0;
  width: 235px;
  border-right: 1px solid #253943;
}

.sidebar .nav .nav-item .nav-link:hover {
  background-color: #253943;
}

.sidebar .nav .nav-item .nav-link:hover .menu-title {
  color: #FFF5D6 !important;
}

.page-body-wrapper {
  max-height: 100vh;
}

.modal-open .sidebar-offcanvas {
  overflow: hidden;
}

.sidebar .nav .nav-item.active>.nav-link {
  background: #253943;
  position: relative;
}

.sidebar .nav .nav-item .nav-link .menu-title {
  color: #253943;
  font-size: 16px;
  font-weight: 400;
  line-height: 19.84px;
  text-align: left;

}

.sidebar .nav .nav-item .nav-link {
  padding: 15px;
  gap: 25px;
}

.nav-link.active-menu .menu-title {
  color: #FFF5D6;
}

.nav-link.active-menu img {
  filter: none;
}

.active-menu {
  background-color: #253943;
}

.nav-link {
  display: flex;
  align-items: center;
  padding: 10px;
}

.menu-icons {
  margin-right: 10px;
}


.logout-btn {
  position: absolute;
  bottom: 20px;
  margin-left: 1rem;
}


.sidebar .nav .nav-item .nav-link i {
  color: #253943;
}

.sidebar .nav .nav-item:hover>.nav-link i,
.sidebar .nav .nav-item:hover>.nav-link .menu-title,
.sidebar .nav .nav-item:hover>.nav-link .menu-arrow {
  color: #FFF5D6 !important;
}



.main-panel {
  background-image: url('../../../assets/images/ELJUNTOBACKGROUND.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  overflow-y: auto;
}

.logOutModal {
  background-color: rgba(0, 0, 0, 0.5);
  color: black !important;
  font-size: 1rem;
}

.footer {
  background-color: #7C9C9A;
  border-top: 1px solid #253943;
}

.footer-text {
  font-family: Libre Baskerville;
  font-size: 20px;
  font-weight: 700;
  line-height: 24.8px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal-content {
  background-color: #FFF5D6 !important;
}