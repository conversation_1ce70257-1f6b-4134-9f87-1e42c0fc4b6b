import { Component, Output, EventEmitter, Input, OnChanges, SimpleChanges } from '@angular/core';
import { FormControl } from '@angular/forms';
import { debounceTime, distinctUntilChanged } from 'rxjs';

@Component({
  selector: 'app-search-box',
  templateUrl: './search-box.component.html',
  styleUrls: ['./search-box.component.scss']
})
export class SearchBoxComponent implements OnChanges {
  @Input() searchTerm: string = ''; 
  searchControl = new FormControl('');

  @Output() searchValue = new EventEmitter<string>();

  constructor() {
    this.searchControl.valueChanges
      .pipe(debounceTime(500), distinctUntilChanged())
      .subscribe((value) => {
        if (value !== this.searchTerm||value=='') { 
          this.searchValue.emit(value || '');
        }
      });
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['searchTerm'] && changes['searchTerm'].currentValue !== undefined) {
      this.searchControl.setValue(changes['searchTerm'].currentValue, { emitEvent: false });
    }
  }
}
