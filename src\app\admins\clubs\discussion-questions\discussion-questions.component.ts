import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { getBreadCrumbModules } from 'src/app/config/commonHelper';
import { moduleTypes } from 'src/app/config/constants';

@Component({
  selector: 'app-discussion-questions',
  templateUrl: './discussion-questions.component.html',
  styleUrls: ['./discussion-questions.component.scss']
})
export class DiscussionQuestionsComponent implements OnInit {
  params: any;
  discussionQuestions:any;
  breadCrumbModules = getBreadCrumbModules(moduleTypes.DISCUSSIONQUESTION);

  constructor(private router:Router) {
    this.params = history.state;
   }

  ngOnInit(): void {
    this.discussionQuestions=this.params.discussionQuestions;
    if(this.discussionQuestions){
      // console.log("this.params",this.discussionQuestions);
    }else{
      this.router.navigate(['/admins/clubs/club']);
    }
  }

}
