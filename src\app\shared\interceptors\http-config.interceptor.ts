import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import {
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor,
  HttpErrorResponse
} from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { ToastrService } from 'ngx-toastr';

@Injectable()
export class HttpConfigInterceptor implements HttpInterceptor {

  constructor(private router: Router, private toastr: ToastrService) {}

  intercept(request: HttpRequest<unknown>, next: HttpHandler): Observable<HttpEvent<unknown>> {
 
    const xAccessTtoken = localStorage.getItem('token');
    
    // Bypass for login requests
    if (request.url.includes('/login') || request.url.includes('openlibrary.org')) {
      return next.handle(request);
    }

    let newHeaders = request.headers;
    const PortalType='ADMIN'
    if (xAccessTtoken) {
      newHeaders = newHeaders.append('Authorization', xAccessTtoken);
      newHeaders = newHeaders.append('Portal-Type', PortalType);
    } else {
      this.router.navigate(['/login']);
    }

    const authReq = request.clone({ headers: newHeaders });

    return next.handle(authReq).pipe(
      catchError((error: HttpErrorResponse) => {
        if (error.status === 401) {
          localStorage.clear();
          this.router.navigate(['/login']);
          this.toastr.error('Your session has expired. Please log in again.');
        }
        return throwError(() => error);
      })
    );
  }
}
