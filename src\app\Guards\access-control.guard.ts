import { Injectable } from '@angular/core';
import { CanActivate, Router, ActivatedRouteSnapshot, RouterStateSnapshot } from '@angular/router';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class AccessControlGuard implements CanActivate {

  constructor(private router: Router) {}

  canActivate(
    route: ActivatedRouteSnapshot,
    state: RouterStateSnapshot): Observable<boolean> | Promise<boolean> | boolean {
    // Get accessMenuList from local storage
    const adminModulesData = localStorage.getItem('adminModules');

    if (adminModulesData !== null) {
      const accessMenuList = JSON.parse(adminModulesData);
      const requestedRoute = state.url;

      // Check if the requested route starts with any component path in accessMenuList
      const hasAccess = accessMenuList.some((menu: { AM_url: string; }) => requestedRoute.startsWith(menu.AM_url));

      if (hasAccess) {
        return true; // Allow navigation if route is accessible
      } else {
        // Redirect to a default page and prevent navigation
        this.router.navigate(['/unauthorized-page']); // Change '/unauthorized' to your unauthorized page
        return false;
      }
    } else {
      console.error('adminModules not found in localStorage');
      this.router.navigate(['/unauthorized-page']); // Change '/unauthorized' to your unauthorized page
      return false; // Prevent navigation
    }
  }
}
