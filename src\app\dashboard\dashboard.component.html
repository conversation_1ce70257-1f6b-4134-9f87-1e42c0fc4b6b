<app-sidebar [menuTitle]="menuTitle">
  <div class="content-wrapper fade-in">
    <div class="row mt-0">
      <div class="col-md-4 mb-2">
        <div class="card info-card">
          <div class="card-body cardContainer">
            <div class="head p-2">
              <h3 class="countNumbers"><a routerLink="/admins/users">{{ totalUsers | number:'1.0-0' }}</a></h3>
              <h6 class="countTitle">Number of users</h6>
            </div>
            <!-- <div class="icon"> -->
            <img class="menu-icons" src="./assets/icons/Users.svg" style="height: 50px; width: 50px;" />
            <!-- </div> -->
          </div>
        </div>
      </div>
      <div class="col-md-4 mb-2">
        <div class="card info-card">
          <div class="card-body cardContainer">
            <div class="head p-2">
              <h3 class="countNumbers"><a routerLink="/admins/clubs">{{ totalBookClubs | number:'1.0-0' }}</a></h3>
              <h6 class="countTitle">Number of bookclubs</h6>
            </div>
            <div class="icon">
              <img class="menu-icons" src="./assets/icons/Clubs.svg" style="height: 50px; width: 50px;" />
            </div>
          </div>
        </div>
      </div>
      <div class="col-md-4 mb-2">
        <div class="card info-card">
          <div class="card-body cardContainer">
            <div class="head p-2">
              <p class="countNumbers"><a routerLink="/admins/book-list">{{ totalBooks | number:'1.0-0' }}</a></p>
              <h6 class="countTitle">Number of books</h6>
            </div>
            <div class="icon">
              <img class="menu-icons" src="./assets/icons/Books.svg" style="height: 50px; width: 50px;" />
            </div>
          </div>
        </div>
      </div>
    </div>


    <div class="row mt-4">
      <div class="col-lg-12">
        <div class="card shadow-sm chart-card py-3">
          <div class="row chart-row">
            <div class="col-lg-12">
              <h4 class="text-center mt-2"></h4>
              <div class="filter-options d-flex justify-content-between align-items-center px-3">
                <div class="text-group insight-text-group">
                  <input type="radio" id="all" name="timeframe" value="all" (change)="onframeChange($event)" checked>
                  <label for="all" class="text-label">Months</label>

                  <input type="radio" id="3-months" name="timeframe" value="3 months" (change)="onframeChange($event)">
                  <label for="3-months" class="text-label">3 Months</label>
                </div>

                <div class="text-group insight-text-group">
                  <input type="radio" id="users" name="userType" value="users" (change)="onTimeframeChange($event)"
                    checked>
                  <label for="users" class="text-label">Users</label>

                  <input type="radio" id="bookClubs" name="userType" value="bookClubs"
                    (change)="onTimeframeChange($event)">
                  <label for="bookClubs" class="text-label">Bookclubs</label>
                </div>
              </div>

              <p-chart class="canvas mt-4" type="line" height="300px" [data]="basicData"
                [options]="basicOptions"></p-chart>


            </div>
          </div>

        </div>
      </div>
    </div>

    <div class="card mt-5 chart-card">
      <div class="card-title heading-text mb-1">Top 5 To-Be-Read Books</div>
      <div class="card-body px-0 pt-0">
        <div class="table-responsive">
          <table class="table color-primary table-sm mb-0">
            <thead>
              <tr>
                <th>
                  Name of the book
                </th>
                <th>
                  Author
                </th>
                <th class="text-right">
                  Count
                </th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let book of toBeReadData">
                <td class="wrap-text">
                  {{book?.bookName}}
                </td>
                <td>
                  {{book?.bookAuthor}}
                </td>
                <td class="text-right">
                  {{book?.userCount}}
                </td>
              </tr>
            </tbody>

          </table>
        </div>

      </div>
    </div>

    <div class="card mt-5 chart-card">
      <div class="card-title heading-text mb-1">Top 5 Currently Reading Books</div>
      <div class="card-body px-0 pt-0">
        <div class="table-responsive">
          <table class="table color-primary table-sm mb-0">
            <thead>
              <tr>
                <th>
                  Name of the book
                </th>
                <th>
                  Author
                </th>
                <th class="text-right">
                  Count
                </th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let book of currentlyReadingData">
                <td class="wrap-text">
                  {{book?.bookName}}
                </td>
                <td>
                  {{book?.bookAuthor}}
                </td>
                <td class="text-right">
                  {{book?.userCount}}
                </td>
              </tr>
            </tbody>

          </table>
        </div>

      </div>
    </div>

    <div class="card mt-5 chart-card">
      <div class="card-title heading-text mb-0">
        Most Popular Books
      </div>
      <div class="card-title heading-text mb-1">
        <div class="row d-flex align-items-center m-0 p-0">
          <div class="col-md-6 m-0 p-0">
            <div class="pickers">
              <div id="picker1" class="picker">
              <label class="label" for="fromDate">From</label>
              <input type="date" [max]="toDate" class="form-control" id="fromDate" (change)="onDateChange()" [(ngModel)]="fromDate">
              </div>
              
              <div id="picker2" class="picker">
              <label class="label" for="toDate">To</label>
              <input type="date" [min]="fromDate" (change)="onDateChange()" class="form-control" id="toDate" [(ngModel)]="toDate">
              </div>
            </div>
          </div>
          <div class="col-lg-6 m-0 p-0 last-res-btn">
            <button type="submit" class="btn btn-primary el-btn mr-2" (click)="getPopularBooks()">Submit</button>
            <button type="button" class="btn btn-outline-primary el-btn" (click)="setDates()">Reset</button>
        </div>
        </div>
      </div>
      <div class="card-body px-0 pt-0">
        <div class="table-responsive">
          <table class="table color-primary table-sm mb-0">
            <thead>
              <tr>
                <th>
                  Name of the book
                </th>
                <th>
                  Author
                </th>
                <th class="text-right">
                  No of meetings
                </th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let book of popularsBooks">
                <td class="wrap-text-1"> 
                  {{ book.book_title }}
                </td>
                <td>
                  {{ book.book_author }}
                </td>
                <td class="text-right">
                  {{ book.meeting_count }}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
 

</app-sidebar>