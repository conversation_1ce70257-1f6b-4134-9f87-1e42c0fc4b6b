<app-sidebar [menuTitle]="menuTitle">
    <div class="content-wrapper fade-in">

        <div class="row mb-4 head-Home">
            <div [ngClass]="[isLargeScreen?'col-lg-9': 'col-lg-8']" style="gap: 25px;"
                class="position-relative d-flex mb-2">
                <div>
                    <app-search-box [searchTerm]="existingSearchTerm" (searchValue)="onSearch($event)"></app-search-box>
                </div>
                <div class="btns-group">
                    <button [disabled]="!searchTerm" type="submit" class="btn btn-primary el-btn" (click)="search()">Search</button>
                    <button *ngIf="!activeTab" [ngClass]="{'active': activeSearchBtn === 'author'}" (click)="setActiveSearchBtn('author')"
                        type="button" class="btn btn-outline-primary filter-btn">Search Author</button>
                    <button *ngIf="!activeTab" [ngClass]="{'active': activeSearchBtn === 'book'}" (click)="setActiveSearchBtn('book')"
                        type="button" class="btn btn-outline-primary filter-btn">Search Book</button>
                </div>
            </div>

            <!-- <div [ngClass]="[isLargeScreen?'col-lg-2': 'col-lg-3']">
                <app-breadcrumb [breadCrumbModules]="breadCrumbModules" activeColor="text-gray-600"
                    inActiveColor="text-gray-400">
                </app-breadcrumb>
            </div> -->

            <div [ngClass]="[isLargeScreen?'col-lg-3': 'col-lg-4']" class="mb-2 last-res-btn">
                <button type="button" class="btn btn-primary el-btn mr-2" (click)="openAddBookModal()">Add
                    Book</button>
                <!-- <button type="button" class="btn btn-primary el-btn mr-2" routerLink="/admins/book-list/add-book" [state]="{isEdit:false}">Add
                 Book</button> -->
                <button [ngClass]="{'bell-icon':showBellIcon}" type="button" class="btn btn-primary request-btn el-btn"
                    routerLink="/admins/book-list/requests">Requests</button>

            </div>
        </div>

        <div class="row mb-4">

            <div [ngClass]="[isLargeScreen?'col-lg-8': 'col-lg-10']" class="filter-btn-row">
                <button [disabled]="!searchTerm"
                    [ngClass]="{'active': activeTab === 'whoIntoThisBook','disable-btn':!searchTerm}"
                    (click)="setActiveTab('whoIntoThisBook')" type="button"
                    class="btn btn-outline-primary filter-btn">Who’s Into This Book?</button>
                <button [disabled]="!searchTerm"
                    [ngClass]="{'active': activeTab === 'whoIntoThisAuthor','disable-btn':!searchTerm}"
                    (click)="setActiveTab('whoIntoThisAuthor')" type="button"
                    class="btn btn-outline-primary filter-btn">Who’s Into This Author?</button>
                <!-- <button [disabled]="!searchTerm"
                    [ngClass]="{'active': activeTab === 'What Clubs Are Reading This?','disable-btn':!searchTerm}"
                    (click)="setActiveTab('What Clubs Are Reading This?')" type="button"
                    class="btn btn-outline-primary filter-btn">What Clubs Are Reading This?</button> -->
            </div>

            <!-- 
            <div [ngClass]="[isLargeScreen?'col-lg-4': 'col-lg-2']" class="donload-btn mt-small-2">
                <button type="button" [ngClass]="{'disable-btn':!isFilterApplied||data.length==0}"
                    [disabled]="!isFilterApplied||data.length==0" (click)="downloadReport()"
                    class="btn btn-primary el-btn">Download</button>
            </div> -->

        </div>
        <div *ngIf="data&&data.length>1&&!activeTab" class="row mb-4">
            <div class="col-lg-12">
                <button  [ngClass]="{'active': isSortApplied}"
                    (click)="sortData()" type="button" class="btn btn-outline-primary filter-btn">Sort A-Z</button>
            </div>
        </div>
        <app-list-with-pagination [perPageItems]="limit" [p]="offset" idKey="bookId" modalTitle="Book" [isSearchApplied]="isSearchApplied"
            [columns]="columns" [data]="data" (openModal)="openAddBookModal($event)" [searchTerm]="searchTerm"
            [actionPermissions]="{ view: false, edit: true, delete: true,members:false }" [showStaticPagination]="showStaticPagination"
            [onCurrentPageChanged]="onCurrentPageChanged" [onDeleteRecord]="deleteRecord" [activeTab]="activeTab"
            [totalNumberOfRecords]="totalNumberOfRecords" [module]="_tomodule" [onPageSizeChanged]="onPageSizeChanged"
            [onEditBook]="getbooksList.bind(this)"></app-list-with-pagination>
    </div>
</app-sidebar>
<app-book #addBookModal (bookSaved)="getbooksList()"></app-book>


<!-- <div class="modal" id="addBookModal">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content el-modal">
            <div class="modal-header" style="border-bottom: none;">
                <h5 class="modal-title w-100 text-center">{{isEdit?'Edit':'Add'}} book</h5>
                <button type="button" (click)="hideModal('addBookModal')" class="close" data-bs-dismiss="modal"
                    style="color: black;">&times;</button>
            </div>
            <form class="forms-sample" (ngSubmit)="onSubmit()" [formGroup]="addNewBookForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-lg-12 mb-3">
                            <app-input-wrapper formControlName="bookName" id="bookName" label="Name of the book"
                                type="text" [isRequired]="true" [isReadonly]="false">
                            </app-input-wrapper>
                        </div>

                        <div class="col-lg-12 mb-3">
                            <app-input-wrapper formControlName="bookAuthor" id="bookAuthor" label="Author" type="text"
                                [isRequired]="true" [isReadonly]="false">
                            </app-input-wrapper>
                        </div>

                    </div>
                </div>
                <div class="modal-footer justify-content-center" style="border-top: none;">
                    <button type="button" class="btn  btn-outline-primary"
                        (click)="hideModal('addBookModal')">Cancel</button>
                    <button type="submit" class="btn btn-primary">Save</button>
                </div>
            </form>

        </div>
    </div>
</div> -->