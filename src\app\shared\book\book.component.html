<div class="modal" id="addBookModal">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content el-modal">
      <div class="modal-header" style="border-bottom: none;">
        <h5 class="modal-title w-100 text-center">{{ isEdit ? 'Edit' : 'Add' }} Book</h5>


        <button type="button" class="close" data-bs-dismiss="modal" (click)="hideModal()"
          style="color: black;">&times;</button>
      </div>
      <form class="forms-sample" (ngSubmit)="onSubmit()" [formGroup]="addNewBookForm">
        <div class="modal-body">
          <div class="row">
            <div class="col-lg-12 mb-3">
              <app-input-wrapper formControlName="bookName" id="bookName" label="Name of the Book" type="text"
                [isRequired]="true" [isReadonly]="false">
              </app-input-wrapper>
            </div>
            <div class="col-lg-12 mb-3">
              <app-input-wrapper formControlName="bookAuthor" id="bookAuthor" label="Author" type="text"
                [isRequired]="true" [isReadonly]="false">
              </app-input-wrapper>
            </div>
          </div>
        </div>
        <div class="modal-footer justify-content-center" style="border-top: none;">
          <button type="button" class="btn btn-outline-primary" (click)="hideModal()">Cancel</button>
          <button type="submit" class="btn btn-primary">{{ isEdit ? 'Update' : 'Save' }}</button>
        </div>
      </form>
    </div>
  </div>
</div>