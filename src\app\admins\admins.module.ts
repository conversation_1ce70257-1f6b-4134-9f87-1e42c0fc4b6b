import { NgModule } from '@angular/core';
import { CommonModule, DatePipe } from '@angular/common';
import { AdminsRoutingModule } from './admins-routing.module';
import { AdminsComponent } from './admins.component';
import { SidebarModule } from '../shared/sidebar/sidebar.module';
import { UsersComponent } from './users/users.component';
import { RoleComponent } from './role/role.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NgxPaginationModule } from 'ngx-pagination';
import { NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { Ng2SearchPipeModule } from 'ng2-search-filter';
import { AddEditUserRoleComponent } from './role/add-edit-user-role/add-edit-user-role.component';
import { AddEditUserComponent } from './users/add-edit-user/add-edit-user.component';
import { CamelCasePipe } from './camel-case-admin.pipe';
import { ListWithPaginationComponent } from '../shared/list-with-pagination/list-with-pagination.component';
import { ProfileComponent } from './users/profile/profile.component';
import { ClubListComponent } from './clubs/club-list/club-list.component';
import { BookListComponent } from './books/book-list/book-list.component';
import { ClubMembersListComponent } from './clubs/club-members-list/club-members-list.component';
import { InputWrapperComponent } from '../shared/input-wrapper/input-wrapper.component';
import { BreadcrumbComponent } from '../shared/breadcrumb/breadcrumb.component';
import { ClubComponent } from './clubs/club/club.component';
import { SearchBoxComponent } from '../shared/search-box/search-box.component';
import { ReportsComponent } from './reports/reports.component';
import { DiscussionQuestionsComponent } from './clubs/discussion-questions/discussion-questions.component';
import { BookRequestsComponent } from './books/book-requests/book-requests.component';
import { BookComponent } from '../shared/book/book.component';
import { BookActivityComponent } from './books/book-activity/book-activity.component';
import { AddBookComponent } from './books/add-book/add-book.component';
import { NgSelectModule } from '@ng-select/ng-select';

@NgModule({
  declarations: [
    AdminsComponent,
    UsersComponent,
    RoleComponent,
    AddEditUserRoleComponent,
    AddEditUserComponent,
    ListWithPaginationComponent,
    CamelCasePipe,
    ProfileComponent,
    ClubListComponent,
    BookListComponent,
    ClubMembersListComponent,
    InputWrapperComponent,
    BreadcrumbComponent,
    ClubComponent,
    SearchBoxComponent,
    ReportsComponent,
    DiscussionQuestionsComponent,
    BookRequestsComponent,
    BookComponent,
    BookActivityComponent,
    AddBookComponent,
  ],
  imports: [
    CommonModule,
    AdminsRoutingModule,
    SidebarModule,
    FormsModule,
    ReactiveFormsModule,
    NgxPaginationModule,
    NgbTooltipModule,
    Ng2SearchPipeModule,
    NgSelectModule
  ],
  exports: [
    ListWithPaginationComponent,
    InputWrapperComponent,
    BreadcrumbComponent,
    SearchBoxComponent,
    BookListComponent
  ],
  providers: [DatePipe], 


})
export class AdminsModule { }
