import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { HttpClient, HttpParams } from '@angular/common/http';
import { tap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root',
})
export class BookClubService {
  private bookClubId = new BehaviorSubject<number | null>(null);
  private bookClubProfileCache: { [key: number]: any } = {};
  private bookClubMembersCache: { [key: string]: any } = {};
  private bookClubMeetingsCache: { [key: string]: any } = {};

  constructor(private http: HttpClient) {}

  // Set BookClubId in localStorage and BehaviorSubject
  setBookClubId(id: number) {
    this.bookClubId.next(id);
    localStorage.setItem('userId', id.toString());
  }

  // Get BookClubId from BehaviorSubject or localStorage
  getBookClubId(): number | null {
    const storedBookClubId = localStorage.getItem('userId');
    return (
      this.bookClubId.value || (storedBookClubId ? +storedBookClubId : null)
    );
  }

  getClubs(payload: any) {
    return this.http.get(`${environment.apiBaseUrl}/book_club/list`, {
      params: {
        search: payload.search,
        limit: payload.limit,
        offset: payload.offset,
      },
    });
  }

  getBookClubDetails(bookClubId: number): Observable<any> {
    if (this.bookClubProfileCache[bookClubId]) {
      return of(this.bookClubProfileCache[bookClubId]);
    }

    let params = new HttpParams()
      .set('bookClubId', bookClubId)
      .set('limit', 10)
      .set('offset', 0);
    return this.http
      .get(`${environment.apiBaseUrl}/book_club/list`, { params })
      /* .pipe(
        tap((response: any) => {
          this.bookClubProfileCache[bookClubId] = response;
        })
      ) */;
  }

  getBookClubMembers(searchFields: any): Observable<any> {
    const cacheKey = `${searchFields.bookClubId}_${searchFields.limit}_${searchFields.offset}`;

    if (this.bookClubMembersCache[cacheKey]) {
      return of(this.bookClubMembersCache[cacheKey]);
    }

    let params = new HttpParams()
      .set('bookClubId', searchFields.bookClubId)
      .set('limit', searchFields.limit)
      .set('offset', searchFields.offset)
      .set('search', searchFields.search ?? '');

    return this.http
      .get(`${environment.apiBaseUrl}/book_club_members/list`, { params })
     /*  .pipe(
        tap((response: any) => {
          if (response.data) {
            this.bookClubMembersCache[cacheKey] = response;
          }
        })
      ) */;
  }

  getBookClubMeetings(searchFields: any): Observable<any> {
    const { bookClubId, limit, offset, meetingType } = searchFields;  
    const cacheKey = `${bookClubId}_${limit}_${offset}_${meetingType}`;  
  
    if (this.bookClubMeetingsCache[cacheKey]) {
      return of(this.bookClubMeetingsCache[cacheKey]);  
    }
  
    let params = new HttpParams()
      .set('bookClubId', bookClubId.toString())  
      .set('limit', limit.toString())
      .set('offset', offset.toString())
      .set('meetingType', meetingType.toString());  
  
    return this.http
      .get(`${environment.apiBaseUrl}/book_club_meeting/meetings`, { params })
      .pipe(
        tap((response: any) => {
          this.bookClubMeetingsCache[cacheKey] = response;  
        })
      );
  }
  
}
