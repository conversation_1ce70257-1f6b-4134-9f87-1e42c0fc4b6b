{"data": [{"id": "1", "Quote": "Incs234", "Product": "Car insurance", "Business": "Business type 1", "Policy": "<PERSON>", "Premium": "$1200", "Status": "In progress", "Updated": "25/04/2020"}, {"id": "2", "Quote": "Incs235", "Product": "Car insurance", "Business": "Business type 2", "Policy": "<PERSON>", "Premium": "$1200", "Status": "Active", "Updated": "25/04/2020"}, {"id": "3", "Quote": "Incs235", "Product": "Car insurance", "Business": "Business type 2", "Policy": "<PERSON>", "Premium": "$1200", "Status": "Expired", "Updated": "25/04/2020"}, {"id": "4", "Quote": "Incs235", "Product": "Car insurance", "Business": "Business type 2", "Policy": "<PERSON>", "Premium": "$1200", "Status": "In progress", "Updated": "25/04/2020"}, {"id": "5", "Quote": "Incs235", "Product": "Car insurance", "Business": "Business type 2", "Policy": "<PERSON>", "Premium": "$1200", "Status": "Active", "Updated": "25/04/2020"}, {"id": "6", "Quote": "Incs235", "Product": "Car insurance", "Business": "Business type 2", "Policy": "<PERSON>", "Premium": "$1200", "Status": "Active", "Updated": "25/04/2020"}, {"id": "7", "Quote": "Incs235", "Product": "Car insurance", "Business": "Business type 2", "Policy": "<PERSON>", "Premium": "$1200", "Status": "Active", "Updated": "25/04/2020"}, {"id": "8", "Quote": "Incs235", "Product": "Car insurance", "Business": "Business type 2", "Policy": "<PERSON>", "Premium": "$1200", "Status": "Expired", "Updated": "25/04/2020"}, {"id": "9", "Quote": "Incs235", "Product": "Car insurance", "Business": "Business type 2", "Policy": "<PERSON>", "Premium": "$1200", "Status": "Active", "Updated": "25/04/2020"}, {"id": "10", "Quote": "Incs235", "Product": "Car insurance", "Business": "Business type 2", "Policy": "<PERSON>", "Premium": "$1200", "Status": "In progress", "Updated": "25/04/2020"}]}