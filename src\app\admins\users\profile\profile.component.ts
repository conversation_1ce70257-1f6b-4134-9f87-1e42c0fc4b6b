import { Component, OnInit } from '@angular/core';
import { NgxSpinnerService } from 'ngx-spinner';
import { User } from '../../Models/userModel';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { formatDateMMMYYYY, getBreadCrumbModules } from 'src/app/config/commonHelper';
import { ProfileService } from 'src/app/shared/services/profile.service';
import { Router } from '@angular/router';
import { moduleTypes,Constants } from 'src/app/config/constants';
import { DatePipe } from '@angular/common';
interface RouteParam {
  id: number;
  view: boolean;
  module: string;
}
@Component({
  selector: 'app-profile',
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.scss'],
  providers:[DatePipe]
})
export class ProfileComponent implements OnInit {
  offset: number = 1;
  limit: number = 10;
  totalNumberOfRecords: number = 0;
  defProfilePicture=Constants.defaulProfilePicture;
  activeTab: string = 'profile';
  params: RouteParam;
  profileDetails = new User();
  profileForm: FormGroup;
  bookcase: any[] = [];
  currentlyReading: any[] = [];
  toBeRead: any[] = [];
  userId: number | null;
  bookcaseColumns: any[] = [
    { title: "Name of the book", dataKey: "bookName" },
    { title: "Author", dataKey: "bookAuthor" },
    // { title: "Top shelf", dataKey: "topShelf" },
    { title: "Completion date", dataKey: "reading_complete_date" },
    { title: "Ratings", dataKey: "ratings" },
    { title: "Review", dataKey: "review" },
  ];

  currentlyReadingColumns: any[] = [
    { title: "Name of the book", dataKey: "bookName" },
    { title: "Author", dataKey: "bookAuthor" },
    // { title: "Top shelf", dataKey: "topShelf" },
   
  ];

  breadCrumbModules: { label: string, path: string, isActive?: boolean }[] = [];
  profilePicture: any;
  constructor(
    private ngxSpinnerService: NgxSpinnerService,
    private router: Router,
    private formBuilder: FormBuilder,
    private toastr: ToastrService,
    private profileService: ProfileService,
    private datePipe:DatePipe
  ) {
    this.initializeForm();
  }

  

  ngOnInit(): void {
    this.params = history.state;
    let module = this.params?.module ?? moduleTypes.USER;
    this.userId = this.params.id || this.profileService.getUserId();;
    if (this.userId) {
      this.profileService.setUserId(this.userId);
      this.getProfileDetails();
    }
    if (module) {
      this.breadCrumbModules = getBreadCrumbModules(module);
    }

  }

  initializeForm() {
    this.profileForm = this.formBuilder.group({
      userName: new FormControl('', []),
      userLocation: new FormControl('', []),
      userEmailId: new FormControl('', []),
      userClubInvitation: new FormControl(false, []),
      userBio: new FormControl('', []),
      userCreatedDate: new FormControl('', []),
      userHandle: new FormControl('', []),


    });
  }

  getProfileDetails() {
    this.ngxSpinnerService.show("globalSpinner");
    try {
      /* this.dataTransferService.getUser(this.params.id).subscribe({
        next: (response: any) => {
          this.profileDetails = response?.data;
          this.profileForm.reset(this.profileDetails);
        },
        error: (err) => {
          console.error('Error while fetching user details:', err);
          this.toastr.error(err.error.message);
          this.ngxSpinnerService.hide("globalSpinner");
          this.profileForm.reset();
        },
        complete: () => {
          this.ngxSpinnerService.hide("globalSpinner");
        }
      }); */
      if (this.userId) {
        this.profileService.getProfileDetails(this.userId).subscribe(
          (profile) => {
            this.profileDetails = profile?.data;
       console.log("this.profileDetails.userCreatedDate",this.profileDetails.userCreatedDate);

            if(this.profileDetails?.userProfilePicture){
              this.profilePicture=Constants.s3BaseUrl+this.profileDetails?.userProfilePicture;           
            }
            if(this.profileDetails?.userCreatedDate){
              this.profileDetails.userCreatedDate=this.datePipe.transform(this.profileDetails.userCreatedDate,'MMMM d, y');           
            }

            this.profileForm.reset(this.profileDetails);
          },
          (error) => {
            console.error('Error fetching profile:', error);
          }
        );
      } else {
        this.toastr.error("Something went wrong");
        this.router.navigate(['admins/users']);
      }

    } catch (err) {
      console.error('Exception while fetching user details:', err);
      this.ngxSpinnerService.hide("globalSpinner");
    } finally {
      this.ngxSpinnerService.hide("globalSpinner");

    }
  }
  getAllReadBooks() {
    try {
      this.ngxSpinnerService.show("globalSpinner");
      this.profileService.getAllReadBooks({ offset: this.offset-1, limit: this.limit, userId: this.userId }).subscribe(
        (bookcase: any) => {
          this.bookcase = bookcase?.data.filter((book: any) => book.is_currently_reading === false).map((book: any) => {
            return {
              ...book,
              topShelf: book.topShelf ? 'Yes' : 'No',
              reading_complete_date: formatDateMMMYYYY(book.reading_complete_date)
            };
          });
          // if(this.bookcase.length===0){
          //   this.toastr.error('No books in bookcase');
          // }
          this.totalNumberOfRecords = bookcase.count;
          console.log("bookcase",this.bookcase);
        //   if(this.bookcase.length===0){
        //   this.toastr.error('No books in bookcase');
        // }
        },
        (error) => {
          this.toastr.error(error?.error?.message);
          console.error('Error fetching bookcase:', error);
        }
      );
    } catch (err) {
      console.error('Exception while fetching bookcase details:', err);
      this.ngxSpinnerService.hide("globalSpinner");
    } finally {
      this.ngxSpinnerService.hide("globalSpinner");
    }

  }

  getCurrentyReadingDetails() {
    try {
      this.ngxSpinnerService.show("globalSpinner");

      this.profileService.getCurrentlyReadingDetails({ offset: this.offset-1, limit: this.limit, userId: this.userId }).subscribe(
        (currentlyReading: any) => {
          this.currentlyReading = currentlyReading?.data.map((book: any) => {
            return {
              ...book,
              topShelf: book.topShelf ? 'Yes' : 'No',
              reading_complete_date: formatDateMMMYYYY(book.reading_complete_date)
            };
          });
          this.totalNumberOfRecords = currentlyReading.count;
        },
        (error) => {
          this.toastr.error(error?.error?.message);
          console.error('Error fetching currentlyReading:', error);
        }
      );
    } catch (err) {
      console.error('Exception while fetching currentlyReading details:', err);
      this.ngxSpinnerService.hide("globalSpinner");
    } finally {
      this.ngxSpinnerService.hide("globalSpinner");
    }

  }

  getTobeReadList() {
    try {
      this.ngxSpinnerService.show("globalSpinner");

      this.profileService.getTobeReadList({ offset: this.offset-1, limit: this.limit, userId: this.userId }).subscribe(
        (toBeRead: any) => {
          this.toBeRead= toBeRead?.data;
          this.totalNumberOfRecords = toBeRead.count;
        },
        (error) => {
          this.toastr.error(error?.error?.message);
          console.error('Error fetching currentlyReading:', error);
        }
      );
    } catch (err) {
      console.error('Exception while fetching currentlyReading details:', err);
      this.ngxSpinnerService.hide("globalSpinner");
    } finally {
      this.ngxSpinnerService.hide("globalSpinner");
    }

  }

  setActiveTab(tabName: string): void {
    this.activeTab = tabName;
    if (this.activeTab === 'bookcase') {
      this.getAllReadBooks();
    }else if(this.activeTab === 'currenltyreading'){
      this.getCurrentyReadingDetails();
    }else if(this.activeTab === 'toBeRead'){
      this.getTobeReadList();
    }else{
      // this.toastr.error("Something went wrong");  
      return
    }
  }

  onCurrentPageChanged = (currentOffset: number) => {
    this.offset = currentOffset ;
    if (this.activeTab === 'bookcase') {
      this.getAllReadBooks();
    }else if(this.activeTab === 'currenltyreading'){
      this.getCurrentyReadingDetails();
    }else if(this.activeTab === 'toBeRead'){
      this.getTobeReadList();
    }else{
      // this.toastr.error("Something went wrong");  
      return
    }  }

  onPageSizeChanged = (pageSizeChanged: number) => {
    this.offset=1;
    this.limit = pageSizeChanged;
    if (this.activeTab === 'bookcase') {
      this.getAllReadBooks();
    }else if(this.activeTab === 'currenltyreading'){
      this.getCurrentyReadingDetails();
    }else if(this.activeTab === 'toBeRead'){
      this.getTobeReadList();
    }else{
      // this.toastr.error("Something went wrong");  
      return
    }  }
}
