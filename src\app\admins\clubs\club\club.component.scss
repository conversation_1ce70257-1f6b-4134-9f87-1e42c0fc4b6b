// .nav-tabs .nav-link {
//     background: transparent;
//     color: #253943;
//     font-weight: 600;
//     border-radius: 4px;
//     border: 1px solid #0000;
// }

// .nav-tabs .nav-link.active,
// .nav-tabs .nav-item.show .nav-link {
//     color: #253943;
//     font-weight: 600;
//     background-color: #fff5d6;
//     border-color: #253943 #9ea0a5 #ffffff;
// }

// .nav-tabs {
//     border-bottom: 1px solid #253943;
// }


// .tab-content {
//     border: none !important;
// }

.table-responsive {
    background: #fff5d6;
    color: #253943;
    border: 1px solid #253943;
    border-radius: 10px;

}

