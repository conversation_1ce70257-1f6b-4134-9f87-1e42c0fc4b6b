import { Injectable } from '@angular/core';

@Injectable()
export class Constants {
    public static JwtSecretKey = 'BB3519EADB472A08D73434F60392684803DE8D1593FE656FD27DC71DA9E5A116';
    public static readonly limit: number = 2147483647;
    public static readonly offset: number = 0;

    public static readonly defaultOffsetLimit: any = {
        limit: Constants.limit,
        offset: Constants.offset
    }

    public static s3BaseUrl:string='https://el-junto.s3.amazonaws.com/'
    public static defaulProfilePicture='./assets/images/Profile_2.png'

}

export class moduleTypes {
    public static readonly USER: string = "USER";
    public static readonly USERPROFILE: string = "USERPROFILE";
    public static readonly CLUBS: string = "CLUBS";
    public static readonly CLUB: string = "CLUB";
    public static readonly CLUBMEMBERS: string = "CLUBMEMBERS";
    public static readonly CLUBMEMBERPROFILE: string = "CLUBMEMBERPROFILE";
    public static readonly DISCUSSIONQUESTION: string = "DISCUSSIONQUESTION";
    public static readonly BOOK: string = "BOOK";
    public static readonly BOOKREQUESTS: string = "BOOKREQUESTS";
    public static readonly BOOKACTIVITY: string = "BOOKACTIVITY";
    public static readonly VERIFYBOOK: string = "VERIFYBOOK";

    public static readonly REPORTS : String = "REPORTS"
}
