import { Component, OnInit } from '@angular/core';
import { ToastrService } from 'ngx-toastr';
import { ActivatedRoute } from '@angular/router';
import { DataTransferService } from 'src/app/shared/services/data-transfer.service';
import { FormControl } from '@angular/forms';
import { NgxSpinnerService } from 'ngx-spinner';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { BookClubService } from 'src/app/shared/services/book-club.service';
import { getBreadCrumbModules } from 'src/app/config/commonHelper';
import { moduleTypes } from 'src/app/config/constants';
interface RouteParam {
  id: number;
  view: boolean;
  module: string;
}
@Component({
  selector: 'app-club-members-list',
  templateUrl: './club-members-list.component.html',
  styleUrls: ['./club-members-list.component.scss']
})
export class ClubMembersListComponent implements OnInit {
  totalNumberOfRecords: number = 0;
  offset: number = 1;
  bookClubMembersList: any[] = [];
  columns: any[] = [
    { title: "Name", dataKey: "userName" },
    { title: "Email", dataKey: "userEmailId" },
  ]
  params: RouteParam;
  searchTerm = '';  
  limit: number = 10;
  menuTitle: string = 'List Of Club Members';
  searchControl: FormControl = new FormControl();
  bookClubId: number | null;
  _module: string = moduleTypes.CLUBMEMBERPROFILE;
  breadCrumbModules = getBreadCrumbModules(moduleTypes.CLUBMEMBERS);
  isLargeScreen: boolean = true;

  constructor(
    private toastr: ToastrService,
    private ngxSpinnerService: NgxSpinnerService,
    private bookClubService: BookClubService
  ) {

  }


  ngOnInit(): void {
    this.params = history.state;
    this.bookClubId = this.params.id || this.bookClubService.getBookClubId();
    if (this.bookClubId) {
      this.bookClubService.setBookClubId(this.bookClubId);
      this.getBookClubMembers();
    }
    this.checkScreenSize();
  }

  private checkScreenSize() {
    this.isLargeScreen = window.innerWidth > 1100;
  }

  onSearch(searchValue: string) {
    this.offset = 1;
    this.searchTerm=searchValue;
    this.getBookClubMembers();
  }

  getBookClubMembers() {
    /* const payload = {
      bookClubId: this.bookClubId,
      search: searchTerm ? searchTerm : "",
      limit: this.limit,
      offset: this.offset
    }
    this.ngxSpinnerService.show('globalSpinner');

    this.dataTransferService.getBookClubMembers(payload).subscribe({
      next: (value: any) => {
        if (value.data) {
          this.data = value.data;
        }
        this.ngxSpinnerService.hide('globalSpinner');
      }, error: (err) => {
        this.toastr.error(err.error.message);
        console.log(err);
        this.ngxSpinnerService.hide('globalSpinner');
      }, complete: () => {
        this.ngxSpinnerService.hide("globalSpinner");
      }
    }); */

    try {
      this.ngxSpinnerService.show("globalSpinner");
      const payload = {
        bookClubId: this.bookClubId,
        search: this.searchTerm,
        limit: this.limit,
        offset: this.offset-1
      }
      this.bookClubService.getBookClubMembers(payload).subscribe(
        (response: any) => {
          /*   this.bookClubMembersList = response?.data; */
          this.totalNumberOfRecords = response?.count;

          this.bookClubMembersList = response?.data.map((member: any) => {
            return {
              ...member,
              userName: member.userType == 'LEADER' ? `${member.userName} (Leader)` : member.userName,
            };
          });
          this.ngxSpinnerService.hide("globalSpinner");
        },
        (error) => {
          this.toastr.error(error?.error?.message);
          console.error('Error fetching club members:', error);
          this.ngxSpinnerService.hide("globalSpinner");

        }
      );
    } catch (err) {
      this.ngxSpinnerService.hide("globalSpinner");
      this.toastr.error("Something went wrong");
      console.error('Exception while fetching club members:', err);
    } 
    // finally {
    //   this.ngxSpinnerService.hide("globalSpinner");
    // }
  }


  onCurrentPageChanged = (currentOffset: number) => {
    this.offset = currentOffset ;
    this.getBookClubMembers()
  }

  onPageSizeChanged = (pageSizeChanged: number) => {
    this.offset=1;
    this.limit = pageSizeChanged;
    this.getBookClubMembers();
  }

  deleteRecord = (elementID: number) => {
    // this.selectedRecordId = elementID;
  }

}
