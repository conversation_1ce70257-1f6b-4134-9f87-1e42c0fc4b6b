<app-sidebar [menuTitle]="menuTitle">
    <div class="content-wrapper fade-in">
        

        <div class="row mb-4 head-Home">
            <div [ngClass]="[isLargeScreen?'col-md-2': 'col-md-3']">
                <app-breadcrumb [breadCrumbModules]="breadCrumbModules">
                </app-breadcrumb>
            </div>

            <div class="col-lg-3 position-relative">
                <app-search-box (searchValue)="onSearch($event)"></app-search-box>
            </div>

            <div [ngClass]="[isLargeScreen?'col-md-4': 'col-md-3']"></div>

            <div class="col-lg-3 text-right mb-2 mb-lg-0">
                <button (click)="showModal('compileEmailModal')" type="submit" class="btn btn-primary el-btn mt-small-3">Send noreply email</button>
            </div>

        </div>

        <app-list-with-pagination [perPageItems]="limit" [p]="offset" idKey="userId" modalTitle="User" [columns]="columns"
            [actionPermissions]="{ view: true, edit: false, delete: true,members:false }" [data]="data"
            [onCurrentPageChanged]="onCurrentPageChanged" [onDeleteRecord]="deleteRecord"
            [onlockUnlockUser]="lockUnlockUser" [totalNumberOfRecords]="totalNumberOfRecords"
            [onPageSizeChanged]="onPageSizeChanged" viewPageLink="/admins/users/profile"
            [module]="_tomodule"></app-list-with-pagination>
       
    </div>
</app-sidebar>



<div class="modal" id="compileEmailModal">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content el-modal" >
            <div class="modal-header" style="border-bottom: none;">
                <h5 class="modal-title w-100 text-center">Send Email</h5>
                <button type="button" (click)="hideModal('compileEmailModal')" class="close" data-bs-dismiss="modal"
                    style="color: black;">&times;</button>
            </div>
            <form class="forms-sample"  [formGroup]="compileEmailForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-lg-12 mb-3">
                            <app-input-wrapper formControlName="subject" id="subject" label="Email subject"
                                type="text" [isRequired]="true" [isReadonly]="false">
                            </app-input-wrapper>
                        </div>

                        <div class="col-lg-12 mb-3">
                            <label class="required-field form-label" for="body">Email body</label>
                            <textarea style="line-height: 1.5;" class="form-control form-text-area" formControlName="body" id="body" rows="10"></textarea>
                        </div>

                    </div>
                </div>
                <div class="modal-footer justify-content-center" style="border-top: none;">
                    <button type="button" class="btn  btn-outline-primary"
                        (click)="hideModal('compileEmailModal')">Cancel</button>
                    <button type="submit" (click)="composeEmails()" class="btn btn-primary">Send</button>
                </div>
            </form>

        </div>
    </div>
</div>