<app-sidebar>

    <div class="content-wrapper fade-in">    
            <div class="row mb-4 head-Home">
            <div class="col-lg-5 mb-2">
                <app-breadcrumb [breadCrumbModules]="breadCrumbModules" activeColor="text-gray-600"
                inActiveColor="text-gray-400">
            </app-breadcrumb>
            </div>

            <div class="col-lg-3 mb-2"></div>
            <div class="col-lg-4 mb-2 text-right">
                <!-- <button type="submit" class="btn btn-primary el-btn mr-2" routerLink="/admins/book-list/add-book" [state]="{isEdit:false}">Add Book</button> -->

            </div>
        </div>

        <app-list-with-pagination [perPageItems]="limit" [p]="offset" idKey="bookId" [columns]="columns" [data]="data"
            [actionPermissions]="{ add: true, edit: true}"
            [onCurrentPageChanged]="onCurrentPageChanged" [onUpdateBookRequestStatus]="updateRequestStatus"
            [totalNumberOfRecords]="totalNumberOfRecords" [module]="_tomodule"
            [onPageSizeChanged]="onPageSizeChanged"></app-list-with-pagination>
    </div>
</app-sidebar>
<app-book #addBookModal (bookSaved)="getBookRequests()"></app-book>