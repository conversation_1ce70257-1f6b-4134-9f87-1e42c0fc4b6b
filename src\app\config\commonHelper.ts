import { formatDate } from '@angular/common';
import { moduleTypes } from './constants';
export function formatDateMMMYYYY(intDatetoFormat: number): string {
    if (!intDatetoFormat) {
        return 'Unknown';
    }

    const datetoFormat = new Date(intDatetoFormat);
    return formatDate(datetoFormat, 'MMM yyyy', 'en-US');
}

export function getBreadCrumbModules(module: string,state?:any) {
    if (module) {
        switch (module) {
            case moduleTypes.USER: return [
                { label: 'Users', path: '/admins/users' },
                { label: 'Users List', path: '', isActive: true }
            ];
            
            case moduleTypes.USERPROFILE: return [
                { label: 'Users', path: '/admins/users' },
                { label: 'Users List', path: '/admins/users' },
                { label: 'User Profile', path: '', isActive: true }
            ];
            case moduleTypes.CLUBS: return [
                { label: 'Clubs', path: '/admins/clubs' },
                { label: 'Clubs List', path: '', isActive: true }
            ];
           
            case moduleTypes.CLUB: return [
                { label: 'Clubs', path: '/admins/clubs' },
                { label: 'Clubs List', path: '/admins/clubs'},
                { label: 'Club Details', path: '', isActive: true }
            ];
            case moduleTypes.DISCUSSIONQUESTION: return [
                { label: 'Clubs', path: '/admins/clubs' },
                { label: 'Clubs List', path: '/admins/clubs'},
                { label: 'Club Details', path: '/admins/clubs/club'},
                { label: 'Discussion Questions', path: '',isActive:true}
            ];
            case moduleTypes.CLUBMEMBERS: return [
                { label: 'Clubs', path: '/admins/clubs' },
                { label: 'Club Members', path: '', isActive: true },
            ];
            case moduleTypes.CLUBMEMBERPROFILE: return [
                { label: 'Clubs', path: '/admins/clubs' },
                { label: 'Club Members', path: '/admins/clubs/members' },
                { label: 'Club Member Profile', path: '', isActive: true }
            ];
            case moduleTypes.REPORTS: return [
                { label: 'Reports', path: '/admins/report' },
                { label: 'Reports', path: '', isActive: true }
            ];
             case moduleTypes.BOOK:return[
                { label:'Books', path:'/admins/book-list'},
                { label:'Book List', path:'', isActive:true}
            ];
            case moduleTypes.BOOKREQUESTS:return[
                { label:'Books', path:'/admins/book-list'},
                { label:'Book List', path:'/admins/book-list'},
                { label:'Book Requests', path:'',isActive:true}
            ];
            case moduleTypes.BOOKACTIVITY:return[
                { label:'Books', path:'/admins/book-list',state:state},
                { label:'Books List', path:'/admins/book-list',state:state},
                { label:'Book Activity', path:'',isActive:true}
            ];
            default: return [];
        }
    } else {
        return []
    }
}