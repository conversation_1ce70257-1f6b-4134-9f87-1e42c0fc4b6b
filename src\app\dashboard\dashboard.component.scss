// .card.tale-bg {
//   background: linear-gradient(to right, #FF6F0B, #FF953F) !important;
//   text-align: center;
// }

.content-wrapper {
  background: transparent !important;

}



// .filter-options {
//   display: flex;
//   justify-content: space-between;
//   align-items: center;
// }


// .user-option-row {
//   display: flex;
//   justify-content: end !important;
//   margin-bottom: 10px !important;
// }

// .dropdown {
//   width: 300px;
//   height: 30px;
//   margin-left: 5px;
//   border-radius: 5px;
//   font-size: 0.9rem;
// }


.chart-row {
  width: 100% !important;
  margin-left: 10px;
  margin-right: 10px;
}


.menu-icons {
  font-size: 30px;
  color: #253943;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  right: 20px;
}

.countNumbers {
  font-size: 20px;
  font-weight: 700;
  line-height: 24.8px;
}


.cardContainer {
  position: relative;
}

// .card-people-wrapper {
//   display: flex;
//   justify-content: center;
//   align-items: center;
//   height: 100%;
// }

.info-card {
  background-color: #FFF5D6 !important;
  height: 110px;
  outline: 1px solid #253943 !important;
}

.countTitle {
  font-weight: 400;
  font-size: 16px;
  line-height: 19.84px;
  color: #253943;
}

.chart-card {
  background-color: #FFF5D6 !important;
  outline: 1px solid #253943 !important;
}

p-chart {
  width: 1080px !important;
}

.text-group {
  display: inline-flex;
}

.text-group input[type="radio"] {
  display: none;
}

.text-group .text-label {
  padding: 10px 6px;
  color: #253943;
  font-size: 20px;
  cursor: pointer;
  margin: 0;
}

// .text-group .text-label:hover {
//   // background-color: #e2e6ea;
// }

.text-group input[type="radio"]:checked+.text-label {
  color: #253943;
  text-decoration: underline;
  font-size: 20px;
  font-weight: 600;
}

.text-group label:first-of-type {
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
}

.text-group label:last-of-type {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
}

.text-group label+label {
  border-left: none;
}


@media (max-width: 320px) {
  p-chart {
    width: 250px !important;
    padding-top: 20px !important;
  }

  .chart-card {
    margin-top: 10px;
  }

  .btn-group .btn {
    padding: 5px 5px;
    border: 1px solid #ccc;
    background-color: #f8f9fa;
    cursor: pointer;
    margin: 0;
  }

  .text-label {
    font-size: 14px !important;
  }

  // .dropdown {
  //   width: 180px;
  //   margin-left: 0px;
  //   margin-bottom: 10px;
  // }

  // .filter-options {
  //   display: flex;
  //   flex-direction: column;
  //   align-items: center;
  // }

  // .dropdown {
  //   height: 23px;
  // }
}

@media (min-width: 321px) and (max-width: 610px) {
  p-chart {
    width: 300px !important;
    overflow-clip-margin: none !important;
    overflow: auto !important;
    font-size: x-small !important;
    padding-top: 20px !important;
  }

  .card-title {
    display: flex;
    flex-direction: column;
  }

  // .dropdown {
  //   height: 23px;
  // }

  .chart-card {
    margin-top: 10px;
    overflow-x: auto;
  }

  .btn-group .btn {
    padding: 5px 5px;
    border: 1px solid #ccc;
    background-color: #f8f9fa;
    cursor: pointer;
    margin: 0;
  }

  .text-label {
    font-size: 14px !important;
  }

  // .dropdown {
  //   width: 180px;
  //   margin-left: 0px;
  //   margin-bottom: 10px;
  // }

  // .filter-options {
  //   display: flex;
  //   flex-direction: column;
  //   align-items: center;
  // }
}



@media (min-width: 611px) and (max-width: 767px) {
  p-chart {
    width: 550px !important;
  }

  .chart-card {
    margin-top: 10px;
  }

  .text-group .text-label {
    padding: 5px 5px;
    border: 1px solid #ccc;
    background-color: #f8f9fa;
    cursor: pointer;
    margin: 0;
  }

  // .dropdown {
  //   width: 180px;
  //   margin-left: 2px;
  // }
}

@media (min-width: 768px) and (max-width: 1146px) {
  p-chart {
    width: 684px !important;
  }

  .head {
    max-width: 130px;
  }


  // .filter-options {
  //   position: absolute;
  //   top: 10px;
  //   right: 25px;
  //   display: flex;
  //   align-items: center;
  //   justify-content: center;
  // }
  .btn-group .btn {
    padding: 5px 5px;
    border: 1px solid #ccc;
    background-color: #f8f9fa;
    cursor: pointer;
    margin: 0;
  }

  // .dropdown {
  //   width: 180px;
  //   margin-left: 2px;
  // }

}

@media (min-width: 1146px) and (max-width: 1382px) {
  p-chart {
    width: 794px !important;
  }
}

@media (min-width: 1383px) and (max-width: 1440px) {
  p-chart {
    width: 1080px !important;
  }

}

.card-title {
  border-bottom: 1px solid #253943;
  text-underline-position: from-font;
  text-decoration-skip-ink: none;
  padding: 12.5px 28px;
  text-align: left;
}

.picker {
  display: flex;
  align-items: center;
  gap: 12px;
  font-family: Libre Baskerville;
  font-size: 16px;
  font-weight: 400;
  line-height: 19.84px;

}

.label{
font-weight: 700;
}

.wrap-text-1 {
  white-space: normal;
  word-wrap: break-word;
  max-width: 450px;
}

.pickers{
  display: flex;
  align-items: center;
  gap: 30px;
}

td,th{
  padding-left: 28px !important;
  padding-right: 28px !important;
}


@media (max-width: 768px) {
  .card-title {
    padding: 12.5px 12px;
  }

  td,th{
    padding-left: 12px !important;
    padding-right: 12px !important;
  } 
 
  .last-res-btn{
    text-align: center;
    margin-top: 10px !important;
  }

  .pickers{
    flex-direction: column;
    justify-content: flex-start;
    gap: 12px;
  }
 
}
