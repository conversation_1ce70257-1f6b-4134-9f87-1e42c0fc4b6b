import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AdminsComponent } from './admins.component';
import { UsersComponent } from './users/users.component';
import { ProfileComponent } from './users/profile/profile.component';
import { ClubListComponent } from './clubs/club-list/club-list.component';
import { BookListComponent } from './books/book-list/book-list.component';
import { ClubMembersListComponent } from './clubs/club-members-list/club-members-list.component';
import { ClubComponent } from './clubs/club/club.component';
import { ReportsComponent } from './reports/reports.component';
import { DiscussionQuestionsComponent } from './clubs/discussion-questions/discussion-questions.component';
import { BookRequestsComponent } from './books/book-requests/book-requests.component';
import { BookActivityComponent } from './books/book-activity/book-activity.component';
import { AddBookComponent } from './books/add-book/add-book.component';
const routes: Routes = [
  { path: '', component: AdminsComponent },
  { path: 'users', component: UsersComponent },
  
  {
    path: 'users',
    children: [
      { path: '', component: ClubListComponent },
      { path: 'profile', component: ProfileComponent },
    ]
  },

  {
    path: 'clubs',
    children: [
      { path: '', component: ClubListComponent },
      { path: 'members', component: ClubMembersListComponent },
      { path: 'club', component: ClubComponent },
      { path: 'profile', component: ProfileComponent },
      { path: 'discussion-questions', component: DiscussionQuestionsComponent },
    ]
  },

  {
    path: 'book-list',
    children: [
      { path: '', component: BookListComponent },
      { path: 'requests', component: BookRequestsComponent },
      { path: 'book-activity', component: BookActivityComponent },
      { path: 'add-book', component: AddBookComponent },
      ]
  },

  {
    path: 'reports', component: ReportsComponent
  }

];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class AdminsRoutingModule { }
