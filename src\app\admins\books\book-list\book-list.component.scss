

.btn-outline-primary:not(:disabled):not(.disabled):active:focus,
.btn-outline-primary:not(:disabled):not(.disabled).active:focus,
.show>.btn-outline-primary.dropdown-toggle:focus {
    box-shadow: none !important;
}



.filter-btn:hover {
  background-color: transparent; 
  color: #253943;
}

.request-btn,bell-icon {
    position: relative;
}

.bell-icon::after {
    content: "!";
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: blue;
    color: white;
    font-size: 18px;
    width: 18px;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.btns-group{
    display: flex;  
    flex-direction: row;
    gap: 10px;
}
