import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent } from '@angular/common/http';
import { Observable } from 'rxjs';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  intercept(req: HttpRequest<any>, next: <PERSON>ttp<PERSON><PERSON><PERSON>): Observable<HttpEvent<any>> {
    
    if (req.url.includes('openlibrary.org')) {
        // Bypass modification for openlibrary requests
        return next.handle(req);
      }
  
      // Add 'Portal-Type' header for other requests
      const modifiedReq = req.clone({
        setHeaders: {
          'Portal-Type': 'ADMIN'
        }
      });
  
      return next.handle(modifiedReq);
    
  }
}