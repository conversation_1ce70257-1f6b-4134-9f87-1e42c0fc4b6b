import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable, of } from 'rxjs';
import { HttpClient, HttpParams } from '@angular/common/http';
import { tap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class ProfileService {
  private userId = new BehaviorSubject<number | null>(null);
  private userProfileCache: { [key: number]: any } = {};
  private bookcaseCache: { [key: string]: any } = {};
  private currentlyRadingCache: { [key: string]: any } = {};
  constructor(private http: HttpClient) { }

  // Set userId in localStorage and BehaviorSubject
  setUserId(id: number) {
    this.userId.next(id);
    localStorage.setItem('userId', id.toString());
  }

  getUserId(): number | null {
    const storedUserId = localStorage.getItem('userId');
    return this.userId.value || (storedUserId ? +storedUserId : null);
  }

  getProfileDetails(userId: number): Observable<any> {
    if (this.userProfileCache[userId]) {
      return of(this.userProfileCache[userId]);
    }
    return this.http.get(`${environment.apiBaseUrl}/users/get/${userId}`).pipe(
      tap((response: any) => {
        this.userProfileCache[userId] = response;
      })
    );
  }


  getAllReadBooks(searchFields: any): Observable<any> {
    let params = new HttpParams()
      .set('userId', searchFields.userId)
      .set('limit', searchFields.limit)
      .set('offset', searchFields.offset)
      .set('completeReading', false)
      .set('toBeRead', false)

    return this.http.get(`${environment.apiBaseUrl}/bookCase/topshelf-list`, { params })

  }

  getCurrentlyReadingDetails(searchFields: any): Observable<any> {
    let params = new HttpParams()
      .set('userId', searchFields.userId)
      .set('limit', searchFields.limit)
      .set('offset', searchFields.offset);

    return this.http.get(`${environment.apiBaseUrl}/bookCase/currently-reading`, { params })
  
  }

  getTobeReadList(searchFields: any): Observable<any> {
    let params = new HttpParams()
      .set('toBeRead', true) 
      .set('userId', searchFields.userId)
      .set('limit', searchFields.limit)
      .set('offset', searchFields.offset);

    return this.http.get(`${environment.apiBaseUrl}/bookCase/toBeRead-list`, { params })
  
  }

}
