<div class="row" *ngIf="data&&data.length > 0">
    <div class="col-md-12">
        <div class="card" [ngClass]="[module ===  moduleTypes.BOOKACTIVITY ? 'no-top-radius' : 'card-border-radius']">
            <div class="card-body px-0 py-0">
                <div class="table-responsive">
                    <table class="table color-primary table-sm mb-0">
                        <thead>
                            <tr>
                                <ng-container *ngFor="let column of columns; let columIndex = index;">
                                    <th *ngIf="column.type !== 'image'" class="py-3"
                                        [ngClass]="{'text-center': column.title === 'Unlock/Lock', 'text-last': getTextLast(column.title)}">
                                        {{column.title}}
                                    </th>
                                    <th *ngIf="column.type === 'image'" class="py-3">
                                        <img width="20" height="20" [src]="column.src" alt="{{column.title}}">
                                    </th>
                                </ng-container>
                                <th style="text-align: end;" class="p-2" *ngIf="actionPermissions&&!activeTab">Action
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr *ngFor="let element of data | paginate: { itemsPerPage:  perPageItems, currentPage: p, totalItems: showStaticPagination?data.length: totalNumberOfRecords };
                                 let indexOfelement = index;">
                                <ng-container *ngFor="let column of columns; let columIndex = index;">
                                    <td [ngClass]="{'text-last': column.title === 'Top shelf', 'cursor-pointer':module===moduleTypes.BOOK && activeTab}"
                                        class="wrap-text p-2"
                                        *ngIf="column.dataKey !== 'userIsLocked'&& column.title != 'Ratings'&& column.title != 'Review'&&column.title != 'Discussion questions'&&column.title != 'Profile Picture'&&column.title != 'Requests'"
                                        (click)="onRowClick(element,column.title)">

                                        {{ element[column.dataKey] }}
                                    </td>
                                    <td *ngIf="column.dataKey ==='userProfilePicture'">
                                        <img
                                            [src]="element[column.dataKey] ? getImg(element[column.dataKey]) : defProfilePicture">
                                    </td>
                                    <td class="wrap-text text-center p-2" *ngIf="column.dataKey === 'userIsLocked'">
                                        <div class="custom-control custom-switch">
                                            <input type="checkbox" class="custom-control-input"
                                                id="switch{{indexOfelement}}" [checked]="element.userIsLocked"
                                                (click)="onSwitchClick($event, element)" />
                                            <label class="custom-control-label" for="switch{{indexOfelement}}"></label>
                                        </div>
                                    </td>
                                    <td style="min-width: 120px;" class="wrap-text p-2"
                                        *ngIf="column.title == 'Ratings'">
                                        <!-- <i *ngFor="let star of getStarsArray(element.ratings); let i = index" class="fa"
                                            [ngClass]="i < element.ratings ? 'fa-star filled-star' : 'fa-star-o'"></i> -->
                                        <i *ngFor="let starType of getStarsArray(element.ratings)" class="fa" [ngClass]="[
                                        starType === 'full' ? 'fas fa-star filled-star' : '',
                                        starType === 'half' ? 'fas fa-star-half-alt half-star' : '',
                                        starType === 'empty' ? 'far fa-star empty-star' : '']">
                                        </i>
                                    </td>
                                    <td class="wrap-text p-2 text-last"
                                        [ngClass]="{'text-start':column.title === 'Requests'}"
                                        *ngIf="column.title == 'Review'||column.title == 'Discussion questions'||column.title == 'Review'||column.title == 'Requests'">
                                        <a href="javascript:void(0);" class="review-link"
                                            (click)="setSelectedElement(element,column.title);">Read</a>
                                    </td>
                                </ng-container>
                                <td style="text-align: end;" class="p-2" *ngIf="actionPermissions &&!activeTab">
                                    <a style="padding-right: 0px !important; margin-right: 0px !important;"
                                        *ngIf="actionPermissions?.members" class="btn my-0 py-0 icon-red">
                                        <i class="fa fa-user-group" (click)="showMembersList(element.bookClubId)"
                                            placement="top" ngbTooltip="Members"></i>
                                    </a>
                                    <!-- <a style="padding-right: 0px !important; margin-right: 0px !important;"
                                        *ngIf="actionPermissions?.add" class="btn my-0 py-0 icon-red">
                                        <i routerLink="/admins/book-list/add-book" [state]="{request:element}"
                                            class="fa fa-plus" placement="top" ngbTooltip="Add book"></i>
                                    </a> -->
                                    <a style="padding-right: 0px !important; margin-right: 0px !important;"
                                        *ngIf="actionPermissions?.add" class="btn my-0 py-0 icon-red">
                                        <i (click)="openAddBookModal()" class="fa fa-plus" placement="top"
                                            ngbTooltip="Add book"></i>
                                    </a>
                                    <a style="padding-right: 0px !important; margin-right: 0px !important;"
                                        *ngIf="actionPermissions?.view" class="btn my-0 py-0 icon-red">
                                        <i class="fa fa-eye" placement="top" ngbTooltip="View"
                                            [routerLink]="viewPageLink"
                                            [state]="{id:element[idKey],view:true,module:module,fromDiscussion:false }"></i>
                                    </a>
                                    <a style="padding-right: 0px !important; margin-right: 0px !important;"
                                        *ngIf="actionPermissions?.edit" class="btn my-0 py-0 icon-red">
                                        <i (click)="onEditClick(element)" class="fa fa-pencil" placement="top"
                                            ngbTooltip="Edit"></i>
                                    </a>
                                    <a style="padding-right: 0px !important; margin-right: 0px !important;"
                                        *ngIf="actionPermissions?.delete" class="btn my-0 py-0 icon-red">
                                        <i (click)="showModal('deleteRecordModal',element[idKey])"
                                            class="fa fa-trash text-danger" placement="top" ngbTooltip="Delete"></i>
                                    </a>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<div class="form-group row pagination" *ngIf="data&&data.length>0">
    <div class="col-md-4 d-flex align-items-center">
        <div class="dropdown-wrapper position-relative">
            <select [disabled]="data && data.length < 10" [ngClass]="{'disable-btn': data && data.length < 9}"
                name="perPageItems" id="perPageItems" class="form-control page-numbers" [(ngModel)]="perPageItems"
                (ngModelChange)="handlePageSizeChange($event)">
                <option value="10">10</option>
                <option value="20">20</option>
                <option value="30">30</option>
                <option value="40">40</option>
            </select>
            <i class="fa-solid fa-chevron-down downArrow"></i>
        </div>
    </div>
    <div class="col-md-8 pagination" style="padding-top: 14px;">
        <pagination-controls class="custom-pagination" (pageChange)="pageChange($event)" previousLabel="&larr;"
            nextLabel="&rarr;">
        </pagination-controls>
    </div>
</div>
<div class="row" *ngIf="shouldShowNoDataMessage()">
    <div class="col-md-12">
        <div class="card" [ngClass]="[module === moduleTypes.BOOKACTIVITY ? 'no-top-radius' : 'card-border-radius']">
            <div class="card-body">
                No data available
            </div>
        </div>
    </div>
</div>

<div class="modal" id="bookReviewModal">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content el-modal">
            <div class="modal-header" style="border-bottom: none;">
                <h5 class="modal-title w-100 text-center">Review</h5>
                <button type="button" (click)="hideModal('bookReviewModal')" class="close" data-bs-dismiss="modal"
                    style="color: black;">&times;</button>
            </div>
            <div class="modal-body">
                <div *ngIf="module !== moduleTypes.BOOKACTIVITY" class="row">
                    <div class="col-md-6 mb-3">
                        <label for="bookName" class="form-label">Name of the book</label>
                        <input type="text" class="form-control readonly" value="{{selectedRow?.bookName}}" readonly />
                    </div>
                    <div class="col-md-6 mb-3">
                        <label for="bookAuthor" class="form-label">Author</label>
                        <input type="text" class="form-control readonly"
                            value="{{selectedRow?.bookAuthor||selectedRow?.bookAuthor}}" readonly />
                    </div>
                </div>
                <div class="mb-3">
                    <label for="review" class="form-label">{{columnName}}</label>
                    <textarea class="form-control form-text-area readonly" rows="7"
                        readonly>{{selectedRow?.review||selectedRow?.bookReview ? selectedRow?.review||selectedRow.bookReview : 'No review' }}</textarea>
                </div>
            </div>
            <div class="modal-footer justify-content-center" style="border-top: none;">
                <button type="button" class="btn btn-primary" data-dismiss="modal"
                    (click)="hideModal('bookReviewModal')">Ok</button>
            </div>
        </div>
    </div>
</div>

<div class="modal" id="requestModal">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content el-modal">
            <div class="modal-header" style="border-bottom: none;">
                <h5 class="modal-title w-100 text-center">Request to add a book</h5>
                <button type="button" (click)="hideModal('requestModal')" class="close" data-bs-dismiss="modal"
                    style="color: black;">&times;</button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-12 mb-3">
                        <label for="bookName" class="form-label">Book info</label>
                        <textarea class="form-control form-text-area readonly" readonly
                            rows="3"> {{selectedRow?.request}}</textarea>
                    </div>
                </div>
            </div>
            <div class="modal-footer justify-content-center" style="border-top: none;">
                <button type="button" class="btn btn-primary" data-dismiss="modal"
                    (click)="hideModal('requestModal')">Ok</button>
            </div>
        </div>
    </div>
</div>

<div class="modal" id="deleteRecordModal">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content el-modal">
            <div class="modal-header" style="border-bottom: none;">
                <h5 class="modal-title w-100 text-center">Are you sure you want to delete this
                    {{modalTitle|lowercase}}?
                </h5>
                <button type="button" (click)="hideModal('deleteRecordModal')" class="close" data-bs-dismiss="modal"
                    style="color: black;">&times;</button>
            </div>
            <div class="modal-footer justify-content-center" style="border-top: none;">
                <button type="button" class="btn  btn-outline-primary"
                    (click)="hideModal('deleteRecordModal')">Cancel</button>
                <button type="submit" (click)="deleteRecord()" class="btn btn-primary">Delete</button>
            </div>
        </div>
    </div>
</div>


<div class="modal" id="lockUnlockModal">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content el-modal">
            <div class="modal-header" style="border-bottom: none;">
                <h5 class="modal-title w-100 text-center">
                    Are you sure you want to {{userData?.userIsLocked?'unlock':'lock'}} this user?
                </h5>
                <button type="button" (click)="hideModal('lockUnlockModal')" class="close" data-bs-dismiss="modal"
                    style="color: black;">&times;</button>
            </div>
            <div class="modal-footer justify-content-center" style="border-top: none;">
                <button type="button" class="btn btn-outline-primary"
                    (click)="hideModal('lockUnlockModal')">Cancel</button>
                <button (click)="lockUnlockUser()" type="submit" class="btn btn-primary">
                    {{ userData?.userIsLocked ? 'Unlock' : 'Lock' }}
                </button>
            </div>
        </div>
    </div>
</div>


<div class="modal" id="updateRequestStatus">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content el-modal">
            <div class="modal-header" style="border-bottom: none;">
                <h5 class="modal-title w-100 text-center">
                    Do you want to mark this request as complete?
                </h5>
                <button type="button" (click)="hideModal('updateRequestStatus')" class="close" data-bs-dismiss="modal"
                    style="color: black;">&times;</button>
            </div>
            <div class="modal-footer justify-content-center" style="border-top: none;">
                <button type="button" class="btn btn-outline-primary"
                    (click)="hideModal('updateRequestStatus')">Cancel</button>
                <button (click)="updateBookRequestStatus()" type="submit" class="btn btn-primary">
                    Complete
                </button>
            </div>
        </div>
    </div>
</div>

<app-book #addBookModal (bookSaved)="logEventAndFetchBooks($event)"></app-book>
