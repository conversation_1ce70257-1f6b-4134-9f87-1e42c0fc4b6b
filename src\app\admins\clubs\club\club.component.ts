import { Component, OnInit } from '@angular/core';
import { NgxSpinnerService } from 'ngx-spinner';
import { BookClub } from '../../Models/clubModel';
import { FormBuilder, FormControl, FormGroup } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { getBreadCrumbModules } from 'src/app/config/commonHelper';
import { BookClubService } from 'src/app/shared/services/book-club.service';
import { Router } from '@angular/router';
import { moduleTypes } from 'src/app/config/constants';
import { DatePipe } from '@angular/common';

interface RouteParam {
  id: number;
  view: boolean;
  module: string;
}
@Component({
  selector: 'app-club',
  templateUrl: './club.component.html',
  styleUrls: ['./club.component.scss'],
  providers: [DatePipe],
})
export class ClubComponent implements OnInit {
  offset: number = 1;
  limit: number = 10;
  totalNumberOfRecords: number = 0;
  activeTab: string = 'clubdetails';
  params: RouteParam;
  clubDetails = new BookClub();
  clubForm: FormGroup;
  clubMeetings: any[] = [];
  clubId: number | null;
  meetingColumns: any[] = [
    { title: 'Name of the book', dataKey: 'bookName' },
    { title: 'Part of the book covered', dataKey: 'partOfBookCovered' },
    { title: 'Meeting start', dataKey: 'meetingStartTime' },
    { title: 'Alerts', dataKey: 'meetingAlerts' },
    // { title: 'Duration', dataKey: 'meetingDuration' },
    { title: 'Discussion questions', dataKey: 'discussionQuestions' },
  ];
  breadCrumbModules = getBreadCrumbModules(moduleTypes.CLUB);
  fromDiscussion: boolean=false;
  constructor(
    private ngxSpinnerService: NgxSpinnerService,
    private router: Router,
    private formBuilder: FormBuilder,
    private toastr: ToastrService,
    private bookClubService: BookClubService,
    private datePipe: DatePipe
  ) {
    this.initializeForm();
  }

  ngOnInit(): void {
    this.params = history.state;
    this.clubId = this.params.id || this.bookClubService.getBookClubId();
    if (this.clubId) {
      this.bookClubService.setBookClubId(this.clubId);
      this.getClubDetails();
    }

    this.fromDiscussion=history.state.fromDiscussion;
    if(this.fromDiscussion){
     this.setActiveTab('meetings');
    }
    // let module = this.params?.module ?? moduleTypes.CLUB;
    
    
  }

  initializeForm() {
    this.clubForm = this.formBuilder.group({
      bookClubName: new FormControl('', []),
      memberReqPrompt: new FormControl('', []),
      clubCharter: new FormControl('', []),
    });
  }

  getClubDetails() {
    this.ngxSpinnerService.show('globalSpinner');
    try {
      if (this.clubId) {
        this.bookClubService.getBookClubDetails(this.clubId).subscribe(
          (response) => {
            this.clubDetails = response?.data[0];
            this.clubForm.reset(this.clubDetails);
          },
          (error) => {
            this.toastr.error(error?.error?.message);
            console.error('Error fetching clubdetails:', error);
          }
        );
      } else {
        this.toastr.error('Something went wrong');
        this.router.navigate(['/admins/clubs']);
      }
    } catch (err) {
      console.error('Exception while fetching club details:', err);
    } finally {
      this.ngxSpinnerService.hide('globalSpinner');
    }
  }

  getMeetingsList() {
    this.ngxSpinnerService.show('globalSpinner');
    const payload = {
      bookClubId: this.clubId,
      limit: this.limit,
      offset: this.offset-1,
      meetingType: '',
    };
    try {
      this.ngxSpinnerService.show('globalSpinner');
      if (this.clubId) {
        this.bookClubService.getBookClubMeetings(payload).subscribe(
          (response: any) => {
            this.clubMeetings = response?.data?.map((meeting: any) => ({
              ...meeting,
              meetingStartTime: this.datePipe.transform(
                meeting?.meetingStartTime,
                'MMMM d, y h:mm a'
              ),
              meetingEndTime: this.datePipe.transform(
                meeting?.meetingEndTime,
                'MMMM d, y h:mm a'
              ),
              meetingAlerts: meeting?.meetingAlerts?.join(', '),
          }));
            this.totalNumberOfRecords = response.count;
          },
          (error) => {
            this.toastr.error(error?.error?.message);
            console.error('Error fetching meetings:', error);
          }
        );
      }
    } catch (err) {
      console.error('Exception while fetching meetings:', err);
    } finally {
      this.ngxSpinnerService.hide('globalSpinner');
    }
  }

  setActiveTab(tabName: string): void {
    this.activeTab = tabName;
    if (this.activeTab === 'meetings') {
      this.getMeetingsList();
    }
    if (this.activeTab === 'clubdetails') {
      this.fromDiscussion=false;
    }
  }

  onCurrentPageChanged = (currentOffset: number) => {
    this.offset = currentOffset ;
    this.getMeetingsList();
  };

  onPageSizeChanged = (pageSizeChanged: number) => {
    this.offset=1;
    this.limit = pageSizeChanged;
    this.getMeetingsList();
  };
}
