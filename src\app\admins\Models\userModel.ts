export class User {
    userId: number;
    userName: string;
    userHandle: string;
    userPhoneNumber: string;
    userEmailId: string;
    portalType: string;
    userIsDeleted: boolean;
    userIsActive: boolean;
    userIsLocked: boolean;
    userLastSuccessLogin: number;
    userCreatedDate: any;
    userUpdatedDate: number;
    user30DayTrialEndDate: number | null;
    userIsSubscriptionActive: boolean | null;
    userSubscriptionStartedDate: number | null;
    userSubscriptionEndedDate: number | null;
    userProfilePicture: string;
    userLocation: string;
    userBio: string;
    userClubInvitation: boolean;
    userProfileCompleted: boolean;

    constructor(user?: User) {
        this.userId = user?.userId ?? 0;
        this.userName = user?.userName ?? "";
        this.userHandle = user?.userHandle ?? "";
        this.userPhoneNumber = user?.userPhoneNumber ?? "";
        this.userEmailId = user?.userEmailId ?? "";
        this.portalType = user?.portalType ?? "";
        this.userIsDeleted = user?.userIsDeleted ?? false;
        this.userIsActive = user?.userIsActive ?? true;
        this.userIsLocked = user?.userIsLocked ?? false;
        this.userLastSuccessLogin = user?.userLastSuccessLogin ?? 0;
        this.userCreatedDate = user?.userCreatedDate ?? 0;
        this.userUpdatedDate = user?.userUpdatedDate ?? 0;
        this.user30DayTrialEndDate = user?.user30DayTrialEndDate ?? null;
        this.userIsSubscriptionActive = user?.userIsSubscriptionActive ?? null;
        this.userSubscriptionStartedDate = user?.userSubscriptionStartedDate ?? null;
        this.userSubscriptionEndedDate = user?.userSubscriptionEndedDate ?? null;
        this.userProfilePicture = user?.userProfilePicture ?? "";
        this.userLocation = user?.userLocation ?? "";
        this.userBio = user?.userBio ?? "";
        this.userClubInvitation = user?.userClubInvitation ?? false;
        this.userProfileCompleted = user?.userProfileCompleted ?? true;
    }
}
