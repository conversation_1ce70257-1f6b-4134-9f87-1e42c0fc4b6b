<app-sidebar>
  <div class="content-wrapper fade-in">

    <div class="mx-3">
    <app-breadcrumb [breadCrumbModules]="breadCrumbModules" activeColor="text-gray-600" inActiveColor="text-gray-400">
    </app-breadcrumb>
    </div>
    <!-- Tabs navigation -->

    <ul class="nav nav-tabs mt-2 px-3" id="myTab" role="tablist">
      <li class="nav-item" role="presentation">
        <button class="nav-link" [ngClass]="{'active': activeTab === 'profile'}"
          (click)="setActiveTab('profile')">Profile</button>
      </li>
      <li class="nav-item mt-smaller-2" role="presentation">
        <button class="nav-link" [ngClass]="{'active': activeTab === 'toBeRead'}"
          (click)="setActiveTab('toBeRead')">To-Be-Read</button>
      </li>
      <li class="nav-item" role="presentation">
        <button class="nav-link" [ngClass]="{'active': activeTab === 'currenltyreading'}"
          (click)="setActiveTab('currenltyreading')">Currently Reading</button>
      </li>
      <li class="nav-item mt-smaller-2" role="presentation">
        <button class="nav-link" [ngClass]="{'active': activeTab === 'bookcase'}"
          (click)="setActiveTab('bookcase')">All Books Read</button>
      </li>
     
    </ul>

    <div class="mt-4">

      <!-- Profile information -->
      <div class="tab-content" id="myTabContent">
        <!-- Bookcase table -->
        <div *ngIf="activeTab === 'profile'" [formGroup]="profileForm" class="tab-pane fade show active" id="profile"
          role="tabpanel">
          <div class="row mt-0 margin-bottom-30 pt-0">
            <div class="col-md-4 imageColumn">
              <img style="border-radius: 50%;" height="50px" width="50px" [src]="profilePicture?profilePicture: defProfilePicture">
              <span class="ml-2">Profile Picture</span>
            </div>            
          </div>
          <div class="row margin-bottom-30">
            <div class="col-md-4 mb-3">
              <app-input-wrapper formControlName="userName" id="userName" label="Name" type="text" [isRequired]="false"
                [isReadonly]="true">
              </app-input-wrapper>
            </div>

            <div class="col-md-4 mb-3">
              <app-input-wrapper formControlName="userLocation" id="userLocation" label="Location" type="text"
                [isRequired]="false" [isReadonly]="true">
              </app-input-wrapper>
            </div>
            <div class="col-md-4 mb-3">
              <app-input-wrapper formControlName="userEmailId" id="userEmailId" label="Email" type="text"
                [isRequired]="false" [isReadonly]="true">
              </app-input-wrapper>
            </div>
</div>

<div class="row margin-bottom-30">
            <div class="col-md-4 mb-3">
              <app-input-wrapper formControlName="userHandle" id="userHandle" label="Handle" type="text"
                [isRequired]="false" [isReadonly]="true">
              </app-input-wrapper>
            </div>

            <div class="col-md-4 mb-3">
              <app-input-wrapper formControlName="userCreatedDate" id="userCreatedDate" label="Joining date" type="text"
                [isRequired]="false" [isReadonly]="true">
              </app-input-wrapper>
            </div>

            <div class="col-md-4">
              <app-input-wrapper formControlName="userClubInvitation" id="userClubInvitation"
                label="Open to invitation" type="text" [isRequired]="false" [isReadonly]="true"
                [booleanDisplayText]="true">
              </app-input-wrapper>
            </div>

          </div>

           

          <!-- Bio section -->
          <div class="mb-3">
            <label for="bio" class="form-label">Bio</label>
            <textarea  class="form-control form-text-area readonly" id="bio" rows="4" formControlName="userBio" readonly></textarea>
          </div>
          
        </div>

        <div *ngIf="activeTab === 'currenltyreading'" class="tab-pane fade show active" id="currenltyreading" role="tabpanel">
          <app-list-with-pagination [perPageItems]="limit" [p]="offset" idKey="bookcase_id" [columns]="currentlyReadingColumns" [data]="currentlyReading"
            [onCurrentPageChanged]="onCurrentPageChanged" [totalNumberOfRecords]="totalNumberOfRecords"
            [onPageSizeChanged]="onPageSizeChanged"></app-list-with-pagination>
          <br>
        </div>

        <div *ngIf="activeTab === 'bookcase'" class="tab-pane fade show active" id="bookcase" role="tabpanel">
          <app-list-with-pagination [perPageItems]="limit" [p]="offset" idKey="bookcase_id" [columns]="bookcaseColumns" [data]="bookcase"
            [onCurrentPageChanged]="onCurrentPageChanged" [totalNumberOfRecords]="totalNumberOfRecords"
            [onPageSizeChanged]="onPageSizeChanged"></app-list-with-pagination>
          <br>
        </div>

        <div *ngIf="activeTab === 'toBeRead'" class="tab-pane fade show active" id="toBeRead" role="tabpanel">
          <app-list-with-pagination [perPageItems]="limit" [p]="offset" idKey="bookcase_id" [columns]="currentlyReadingColumns" [data]="toBeRead"
            [onCurrentPageChanged]="onCurrentPageChanged" [totalNumberOfRecords]="totalNumberOfRecords"
            [onPageSizeChanged]="onPageSizeChanged"></app-list-with-pagination>
          <br>
        </div>
      </div>
    </div>
  </div>
  <!-- </div> -->
</app-sidebar>