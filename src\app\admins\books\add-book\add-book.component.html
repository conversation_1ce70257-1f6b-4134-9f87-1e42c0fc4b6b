<app-sidebar [menuTitle]="menuTitle">
    <div class="content-wrapper fade-in">
        <div class="row mb-5">
            <div class="col-lg-12">
                <button class="btn btn-primary el-btn" (click)="backNavigation()" type="button">Back</button>
            </div>

        </div>
        <div *ngIf="!showAddBook&&!isEdit" class="verify-page">

            <div class="heading-text d-block">Verify if the book is present in current database</div>
            <div *ngIf="request" class="d-inline-block mt-4">
                <span style="overflow: auto;" class="form-control font-weight-bold">Request: {{request}}</span>
            </div>
            <div class="row my-4">
                <div class="col-lg-5">
                    <label for="searchTerm">Name of the book or author</label>
                    <input class="form-control" type="text" [(ngModel)]="searchTerm" id="searchTerm">
                </div>
                <div class="col-lg-7 mt-small-2 d-flex justify-content-between align-items-end">
                    <!-- Button aligned to the start -->
                    <button [ngClass]="{'disable-btn':!searchTerm}" [disabled]="!searchTerm"
                        class="btn btn-primary el-btn" type="submit" (click)="verifyBook()">Submit</button>
                    <!-- Button aligned to the end -->
                    <button class="btn btn-primary el-btn" type="button" (click)="onNextClick()">Proceed to add book</button>
                </div>
            </div>


            <app-list-with-pagination [perPageItems]="limit" [p]="offset" idKey="bookId" modalTitle="Book"
                [columns]="columns" [data]="data" [searchTerm]="searchTerm"
                [onCurrentPageChanged]="onCurrentPageChanged" [onPageSizeChanged]="onPageSizeChanged"
                [totalNumberOfRecords]="totalNumberOfRecords" [actionPermissions]="false" [module]="_tomodule">
            </app-list-with-pagination>

            <!-- <div class="row mt-2">
                <div class="col-lg-12 text-last">
                    <button class="btn btn-primary el-btn" type="button" (click)="onNextClick()">Next</button>
                </div>
            </div> -->
        </div>

        <div *ngIf="showAddBook||isEdit" class="add-book-page">
            <div [ngClass]="{'mb-4':isEdit}" class="heading-text d-block">{{isEdit?'Edit':'Add'}} Book</div>
            <div class="d-block mt-4">
                <i class="fa-solid fa-triangle-exclamation mr-2"></i>
                <span class="font-weight-bold">Note: Make sure the book being added is not present in the database to prevent duplicates.</span>
            </div>
            <div *ngIf="!isEdit" class="row my-4">
                <div class="col-lg-5">
                    <label for="searchTerm">Search in open library database</label>
    
                    <ng-select class="hide-arrow position-relative hide-arrow position-relative custom-ng-select" [items]="bookData" bindLabel="displayName" [searchable]="true" [loading]="loading"
                        placeholder="Search for a book..." (change)="onSelectBook($event)"
                        (search)="onSearch($event.term)">
                    </ng-select>
                    <img class="searchIcon" src="./assets/icons/Search.svg">

                </div>
            </div>
            <form class="forms-sample" (ngSubmit)="onSubmit()" [formGroup]="addNewBookForm">
                <div class="row">
                    <div class="col-lg-10">
                        <div class="row">
                            <div class="col-lg-4 mb-3">
                                <app-input-wrapper formControlName="bookName" id="bookName" label="Name of the book"
                                    type="text" [isRequired]="true" [isReadonly]="false"></app-input-wrapper>
                            </div>
                            <div class="col-lg-4 mb-3">
                                <app-input-wrapper formControlName="bookAuthor" id="bookAuthor" label="Author"
                                    type="text" [isRequired]="true" [isReadonly]="false"></app-input-wrapper>
                            </div>
                            <div class="col-lg-4 mb-3">
                                <app-input-wrapper formControlName="book_isbn" id="book_isbn" label="ISBN" type="text"
                                    [isRequired]="false" [isReadonly]="isEdit"></app-input-wrapper>
                            </div>
                        </div>
                    </div>
                    <div style="display: flex; align-items: end;" class="col-lg-2">
                        <div class="row">
                            <div class="col-lg-12 mb-3">
                                <button class="btn btn-primary el-btn"
                                    type="submit">{{isEdit?'Update':'Add Book'}}</button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>

    </div>
</app-sidebar>