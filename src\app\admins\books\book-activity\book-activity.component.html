<app-sidebar [menuTitle]="menuTitle">
    <div class="content-wrapper fade-in">
        <div class="row mb-4 head-Home">
            <div [ngClass]="[isLargeScreen?'col-md-3': 'col-md-4']">
                <app-breadcrumb [breadCrumbModules]="breadCrumbModules" activeColor="text-gray-600"
                inActiveColor="text-gray-400">
               </app-breadcrumb>
            </div>
            
            <!-- <div class="position-relative col-md-3 mb-2">
                <app-search-box (searchValue)="onSearch($event)"></app-search-box>
            </div> -->

        </div>
        <div class="row my-3">
            <div class="col-lg-12 nameOrBtnRow">
                <div class="form-control book-or-club-name mt-1">
                    {{ bookOrClubName }}
                </div>
                <div class="download-btn">
                    <button type="button" [ngClass]="{'disable-btn': data.length == 0}"
                        [disabled]="data.length == 0" (click)="downloadReport()"
                        class="btn btn-primary el-btn mt-smaller-2">Download</button>
                </div>
            </div>           
        </div>

        <!-- <div class="row  mb-5">
            <div [ngClass]="[isLargeScreen?'col-md-8': 'col-md-10']" class="filter-btn-row">
                <button 
                    [ngClass]="{'active': activeTab === 'Who’s Read This?'}"
                    (click)="setActiveTab('Who’s Read This?')" type="button"
                    class="btn btn-outline-primary filter-btn">Who’s Read This?</button>
                <button
                    [ngClass]="{'active': activeTab === 'Who’s Reading This?'}"
                    (click)="setActiveTab('Who’s Reading This?')" type="button"
                    class="btn btn-outline-primary filter-btn">Who’s Reading This?</button>
                <button 
                    [ngClass]="{'active': activeTab === 'What Clubs Are Reading This?'}"
                    (click)="setActiveTab('What Clubs Are Reading This?')" type="button"
                    class="btn btn-outline-primary filter-btn">What Clubs Are Reading This?</button>
            </div>
        </div> -->

     
        <div class="row mt-5 mx-0 px-0">
            <div class="col-lg-12 listHeader">
                <div class="row m-0 p-0">
                    <div class="col-lg-5 m-0 p-0">
                        <div class="position-relative">
                            <select [(ngModel)]="filterType" (change)="getFilterData()" class="form-control page-numbers"
                            style="border: 1px solid #253943 !important; color: #253943;">
                            <option style="font-size:16px !important;" *ngFor="let filter of filtersData" [value]="filter.value">{{filter.name}}</option>
                        </select>
                            <i class="fa-solid fa-chevron-down downArrow"></i>
                
                        </div>
                    </div>
                    
                </div>
            </div>
        </div>

        <app-list-with-pagination [perPageItems]="limit" [p]="offset" [columns]="columns" [data]="data"
            [actionPermissions]="{ view: false, edit: false, delete: false,members:false }"
            [showStaticPagination]="showStaticPagination" [activeTab]="activeTab"
            [totalNumberOfRecords]="totalNumberOfRecords" [module]="_tomodule"></app-list-with-pagination>

    </div>
</app-sidebar>
