<app-sidebar>
    <div class="content-wrapper fade-in">
        <!-- <div class="row mx-2">
            <form [formGroup]="searchForm" (ngSubmit)="onSubmit()" class="w-100">
                <div class="row">
                    <div class="col-md-3">
                        <label class="mb-2" for="dropdown1">Report for</label>
                        <div class="dropdown-container">
                            <select id="dropdown1" class="form-control report-dropdown" formControlName="option1">
                                <option *ngFor="let option of dropdownOptions1" [value]="option.value">{{ option.label }}</option>
                            </select>
                            <img class="dropdownArrow" src="./assets/icons/dropdownArraow.svg" alt="Dropdown Arrow">
                        </div>
                    </div>
                    
                  
                </div>

                <div class="row mt-4">
                    <div class="col-md-3">
                        <label class="mb-2" for="dropdown1">Period</label>
                        <div class="dropdown-container">
                           <input type="date" class="form-control" formControlName="option1">
                            <img class="dropdownArrow" src="./assets/icons/dropdownArraow.svg" alt="Dropdown Arrow">
                        </div>
                    </div>
                    
                    <div class="col-md-3 d-flex align-items-end">
                        <button type="button" class="btn el-btn btn-primary mr-2">Submit</button>
                        <button type="button" class="btn el-btn btn-outline-primary">Reset</button>

                    </div>
                    <div class="col-md-3"></div>
                    <div class="col-md-3 d-flex justify-content-end align-items-end">
                        <button type="button" class="btn el-btn btn-primary mr-2">Download</button>
                    </div>
                </div>
            </form>

        </div> -->

       
    </div>
   
</app-sidebar>

